<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PCB Viewer - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .pcb-container {
            padding: 100px 0 50px;
            background: #f8f9fa;
        }

        .pcb-viewer {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .pcb-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .layer-selector {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .layer-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .layer-btn.active {
            background: #667eea;
            color: white;
        }

        .pcb-display {
            background: #2c3e50;
            border-radius: 10px;
            padding: 2rem;
            min-height: 500px;
            position: relative;
            overflow: hidden;
        }

        .pcb-board {
            background: #1a5f1a;
            border-radius: 10px;
            width: 80%;
            height: 400px;
            margin: 0 auto;
            position: relative;
            border: 3px solid #0f3f0f;
        }

        .component {
            position: absolute;
            background: #333;
            border: 1px solid #555;
            border-radius: 3px;
            color: white;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .component:hover {
            background: #667eea;
            transform: scale(1.1);
            z-index: 10;
        }

        .component.ic {
            background: #2c3e50;
            border: 2px solid #34495e;
        }

        .component.resistor {
            background: #8b4513;
            border: 1px solid #654321;
        }

        .component.capacitor {
            background: #4a4a4a;
            border: 1px solid #666;
        }

        .component.connector {
            background: #ffd700;
            color: #333;
            border: 1px solid #daa520;
        }

        .trace {
            position: absolute;
            background: #ffd700;
            z-index: 1;
        }

        .trace.horizontal {
            height: 2px;
        }

        .trace.vertical {
            width: 2px;
        }

        .via {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #c0c0c0;
            border: 1px solid #999;
            border-radius: 50%;
            z-index: 2;
        }

        .bom-table {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .bom-table table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .bom-table th,
        .bom-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .bom-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .bom-table tr:hover {
            background: #f8f9fa;
        }

        .component-details {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .detail-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .detail-card h4 {
            color: #333;
            margin-bottom: 1rem;
        }

        .spec-table {
            width: 100%;
        }

        .spec-table td {
            padding: 0.25rem 0;
            border: none;
        }

        .spec-table td:first-child {
            font-weight: 600;
            color: #666;
            width: 40%;
        }

        .layer-info {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            color: white;
        }

        .zoom-controls {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #333;
        }

        .zoom-btn:hover {
            background: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="#pcb" class="nav-link active">PCB Viewer</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- PCB Viewer Content -->
    <div class="pcb-container">
        <div class="container">
            <h1 class="section-title">PCB Design Viewer</h1>

            <!-- PCB Viewer -->
            <div class="pcb-viewer">
                <h2>Interactive PCB Layout</h2>

                <div class="pcb-controls">
                    <div class="layer-selector">
                        <button class="layer-btn active" onclick="showLayer('top')">Top Layer</button>
                        <button class="layer-btn" onclick="showLayer('bottom')">Bottom Layer</button>
                        <button class="layer-btn" onclick="showLayer('silkscreen')">Silkscreen</button>
                        <button class="layer-btn" onclick="showLayer('soldermask')">Solder Mask</button>
                        <button class="layer-btn" onclick="showLayer('drill')">Drill Holes</button>
                    </div>

                    <div style="margin-left: auto;">
                        <button class="btn btn-outline" onclick="exportGerber()">
                            <i class="fas fa-download"></i> Export Gerber
                        </button>
                    </div>
                </div>

                <div class="layer-info" id="layerInfo">
                    <strong>Top Layer:</strong> Component placement and top copper traces
                </div>

                <div class="pcb-display" id="pcbDisplay">
                    <div class="zoom-controls">
                        <button class="zoom-btn" onclick="zoomIn()">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="zoom-btn" onclick="zoomOut()">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button class="zoom-btn" onclick="resetZoom()">
                            <i class="fas fa-home"></i>
                        </button>
                    </div>

                    <div class="pcb-board" id="pcbBoard">
                        <!-- Components will be dynamically placed here -->
                    </div>
                </div>
            </div>

            <!-- Component Details -->
            <div class="component-details">
                <h2>Component Specifications</h2>
                <div class="details-grid" id="componentDetailsGrid">
                    <!-- Component details will be populated here -->
                </div>
            </div>

            <!-- Bill of Materials -->
            <div class="bom-table">
                <h2>Bill of Materials (BOM)</h2>
                <div class="bom-summary">
                    <div class="bom-stats">
                        <div class="stat-item">
                            <span class="stat-value">16</span>
                            <span class="stat-label">Total Components</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">$24.50</span>
                            <span class="stat-label">Estimated Cost</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">43.2mW</span>
                            <span class="stat-label">Power Consumption</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">4</span>
                            <span class="stat-label">PCB Layers</span>
                        </div>
                    </div>
                </div>
                <table id="bomTable">
                    <thead>
                        <tr>
                            <th>Designator</th>
                            <th>Component</th>
                            <th>Value</th>
                            <th>Package</th>
                            <th>Quantity</th>
                            <th>Manufacturer</th>
                            <th>Part Number</th>
                            <th>Unit Cost</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- BOM data will be populated here -->
                    </tbody>
                </table>
            </div>

            <!-- PCB Manufacturing Specifications -->
            <div class="component-details">
                <h2>PCB Manufacturing Specifications</h2>
                <div class="details-grid">
                    <div class="detail-card">
                        <h4>Board Specifications</h4>
                        <table class="spec-table">
                            <tr><td>Board Size:</td><td>50mm × 75mm</td></tr>
                            <tr><td>Board Thickness:</td><td>1.6mm (±10%)</td></tr>
                            <tr><td>Copper Weight:</td><td>1oz (35μm)</td></tr>
                            <tr><td>Surface Finish:</td><td>HASL Lead-Free</td></tr>
                            <tr><td>Solder Mask:</td><td>Green, Matte</td></tr>
                            <tr><td>Silkscreen:</td><td>White, Both Sides</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>Design Rules</h4>
                        <table class="spec-table">
                            <tr><td>Min Trace Width:</td><td>0.1mm (4 mil)</td></tr>
                            <tr><td>Min Trace Spacing:</td><td>0.1mm (4 mil)</td></tr>
                            <tr><td>Min Via Size:</td><td>0.2mm (8 mil)</td></tr>
                            <tr><td>Via Drill Size:</td><td>0.1mm (4 mil)</td></tr>
                            <tr><td>Min Annular Ring:</td><td>0.05mm (2 mil)</td></tr>
                            <tr><td>Impedance Control:</td><td>50Ω ±10%</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>Layer Stack-up</h4>
                        <table class="spec-table">
                            <tr><td>Layer 1 (Top):</td><td>Signal + Components</td></tr>
                            <tr><td>Layer 2:</td><td>Ground Plane</td></tr>
                            <tr><td>Layer 3:</td><td>Power Plane (+9V, -9V)</td></tr>
                            <tr><td>Layer 4 (Bottom):</td><td>Signal + Routing</td></tr>
                            <tr><td>Dielectric:</td><td>FR-4, εr = 4.5</td></tr>
                            <tr><td>Core Thickness:</td><td>0.2mm each</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>Assembly Information</h4>
                        <table class="spec-table">
                            <tr><td>SMD Components:</td><td>12 (Top Side)</td></tr>
                            <tr><td>Through-hole:</td><td>4 (Both Sides)</td></tr>
                            <tr><td>Reflow Profile:</td><td>SAC305 Lead-Free</td></tr>
                            <tr><td>Peak Temperature:</td><td>245°C</td></tr>
                            <tr><td>Assembly Class:</td><td>IPC Class 2</td></tr>
                            <tr><td>Test Points:</td><td>8 (Accessible)</td></tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Signal Integrity Analysis -->
            <div class="component-details">
                <h2>Signal Integrity & EMC Analysis</h2>
                <div class="details-grid">
                    <div class="detail-card">
                        <h4>Power Distribution</h4>
                        <table class="spec-table">
                            <tr><td>Power Planes:</td><td>Dedicated +9V, -9V, GND</td></tr>
                            <tr><td>Decoupling:</td><td>100nF ceramic (each IC)</td></tr>
                            <tr><td>Bulk Capacitors:</td><td>10μF tantalum</td></tr>
                            <tr><td>Power Ripple:</td><td><50mVpp</td></tr>
                            <tr><td>Ground Bounce:</td><td><10mV</td></tr>
                            <tr><td>PDN Impedance:</td><td><1Ω @ 1MHz</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>EMC Considerations</h4>
                        <table class="spec-table">
                            <tr><td>Guard Rings:</td><td>Around sensitive analog</td></tr>
                            <tr><td>Ground Stitching:</td><td>Via spacing <λ/20</td></tr>
                            <tr><td>Clock Routing:</td><td>Differential, length matched</td></tr>
                            <tr><td>Shielding:</td><td>Copper pour on all layers</td></tr>
                            <tr><td>Ferrite Beads:</td><td>Power supply filtering</td></tr>
                            <tr><td>ESD Protection:</td><td>TVS diodes on inputs</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>Thermal Management</h4>
                        <table class="spec-table">
                            <tr><td>Thermal Vias:</td><td>Under power components</td></tr>
                            <tr><td>Copper Pour:</td><td>Maximum coverage</td></tr>
                            <tr><td>Component Spacing:</td><td>Adequate airflow</td></tr>
                            <tr><td>Max Junction Temp:</td><td>125°C</td></tr>
                            <tr><td>Ambient Operating:</td><td>0°C to +70°C</td></tr>
                            <tr><td>Thermal Resistance:</td><td>θJA = 45°C/W</td></tr>
                        </table>
                    </div>

                    <div class="detail-card">
                        <h4>Testing & Validation</h4>
                        <table class="spec-table">
                            <tr><td>In-Circuit Test:</td><td>Boundary scan</td></tr>
                            <tr><td>Functional Test:</td><td>ECG signal injection</td></tr>
                            <tr><td>EMI Testing:</td><td>CISPR 11 Class B</td></tr>
                            <tr><td>ESD Testing:</td><td>IEC 61000-4-2</td></tr>
                            <tr><td>Vibration:</td><td>IEC 60068-2-6</td></tr>
                            <tr><td>Temperature Cycling:</td><td>IEC 60068-2-14</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Component Info Modal -->
    <div id="componentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody">
                <!-- Component details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    <script src="../data/ecg-data.js"></script>
    <script>
        let currentLayer = 'top';
        let zoomLevel = 1;

        // PCB component data
        const pcbComponents = [
            { id: 'U1', type: 'ic', name: 'INA128UA', x: 40, y: 30, width: 20, height: 15 },
            { id: 'U2', type: 'ic', name: 'OPA2131UA', x: 40, y: 60, width: 20, height: 15 },
            { id: 'U3', type: 'ic', name: 'LM358', x: 40, y: 90, width: 20, height: 15 },
            { id: 'U4', type: 'ic', name: 'OP07D', x: 40, y: 120, width: 20, height: 15 },
            { id: 'U5', type: 'ic', name: 'A0509S-1WR3', x: 10, y: 30, width: 25, height: 20 },
            { id: 'R1', type: 'resistor', name: '1kΩ', x: 70, y: 35, width: 12, height: 5 },
            { id: 'R2', type: 'resistor', name: '1kΩ', x: 70, y: 45, width: 12, height: 5 },
            { id: 'R3', type: 'resistor', name: '10kΩ', x: 70, y: 55, width: 12, height: 5 },
            { id: 'C1', type: 'capacitor', name: '1μF', x: 85, y: 35, width: 8, height: 8 },
            { id: 'C2', type: 'capacitor', name: '1μF', x: 85, y: 45, width: 8, height: 8 },
            { id: 'C3', type: 'capacitor', name: '1μF', x: 85, y: 55, width: 8, height: 8 },
            { id: 'J1', type: 'connector', name: 'Header', x: 10, y: 10, width: 15, height: 8 },
            { id: 'J2', type: 'connector', name: 'Audio Jack', x: 10, y: 150, width: 12, height: 12 }
        ];

        // BOM data with cost information
        const bomData = [
            { designator: 'U1', component: 'Instrumentation Amplifier', value: 'INA128UA/2K5', package: 'SOIC-8', quantity: 1, manufacturer: 'Texas Instruments', partNumber: 'INA128UA/2K5', unitCost: '$3.45' },
            { designator: 'U2', component: 'Operational Amplifier', value: 'OPA2131UA/2K5', package: 'SOIC-8', quantity: 1, manufacturer: 'Texas Instruments', partNumber: 'OPA2131UA/2K5', unitCost: '$2.80' },
            { designator: 'U3', component: 'Operational Amplifier', value: 'LM358', package: 'SOP-8', quantity: 1, manufacturer: 'Various', partNumber: 'LM358', unitCost: '$0.25' },
            { designator: 'U4', component: 'Operational Amplifier', value: 'OP07D', package: 'SOP-8', quantity: 1, manufacturer: 'Various', partNumber: 'OP07D', unitCost: '$1.20' },
            { designator: 'U5', component: 'DC-DC Converter', value: 'A0509S-1WR3', package: 'SIP-4', quantity: 1, manufacturer: 'MORNSUN', partNumber: 'A0509S-1WR3', unitCost: '$8.50' },
            { designator: 'R1-R15', component: 'Resistor', value: '1kΩ', package: 'R1206', quantity: 7, manufacturer: 'Bourns', partNumber: 'CR1206-FX-1001ELF', unitCost: '$0.02' },
            { designator: 'R3', component: 'Resistor', value: '10kΩ', package: 'R1206', quantity: 1, manufacturer: 'Bourns', partNumber: 'CR1206-FX-1002ELF', unitCost: '$0.02' },
            { designator: 'R4,R5', component: 'Resistor', value: '390kΩ', package: 'R1206', quantity: 2, manufacturer: 'Bourns', partNumber: 'CR1206-FX-3903ELF', unitCost: '$0.02' },
            { designator: 'R6', component: 'Resistor', value: '3MΩ', package: 'R1206', quantity: 1, manufacturer: 'Bourns', partNumber: 'CR1206-JW-305ELF', unitCost: '$0.03' },
            { designator: 'R7,R8,R11,R12', component: 'Resistor', value: '1.6kΩ', package: 'R1206', quantity: 4, manufacturer: 'Bourns', partNumber: 'CR1206-FX-1601ELF', unitCost: '$0.02' },
            { designator: 'R16', component: 'Variable Resistor', value: '15kΩ', package: 'SMD', quantity: 1, manufacturer: 'Hokuriku', partNumber: 'VG039NCHXTB153', unitCost: '$0.85' },
            { designator: 'C1-C5', component: 'Capacitor', value: '1μF', package: 'C1206', quantity: 5, manufacturer: 'KEMET', partNumber: 'C1206C105K3NACTU', unitCost: '$0.08' },
            { designator: 'C6', component: 'Capacitor', value: '4.7μF', package: 'CASE-A_3216', quantity: 1, manufacturer: 'Various', partNumber: 'CASE-A_3216', unitCost: '$0.12' },
            { designator: 'C7,C8', component: 'Capacitor', value: '1μF', package: 'CASE-A_3216', quantity: 2, manufacturer: 'Various', partNumber: 'CASE-A_3216', unitCost: '$0.10' },
            { designator: 'J1', component: 'Header', value: '1x5', package: 'HDR-M-2.54', quantity: 1, manufacturer: 'Various', partNumber: 'C358687', unitCost: '$0.15' },
            { designator: 'J2', component: 'Audio Jack', value: '3.5mm', package: 'AUDIO-PJ320', quantity: 1, manufacturer: 'BOOMELE', partNumber: 'C18166', unitCost: '$0.45' }
        ];

        function initializePCBViewer() {
            renderPCBComponents();
            populateBOM();
            populateComponentDetails();
        }

        function renderPCBComponents() {
            const board = document.getElementById('pcbBoard');
            board.innerHTML = '';

            // Add components
            pcbComponents.forEach(comp => {
                const element = document.createElement('div');
                element.className = `component ${comp.type}`;
                element.id = comp.id;
                element.textContent = comp.id;
                element.style.left = comp.x + '%';
                element.style.top = comp.y + '%';
                element.style.width = comp.width + 'px';
                element.style.height = comp.height + 'px';

                element.addEventListener('click', () => showComponentInfo(comp));
                board.appendChild(element);
            });

            // Add some traces (simplified)
            addTraces();
            addVias();
        }

        function addTraces() {
            const board = document.getElementById('pcbBoard');

            // Horizontal traces
            const traces = [
                { x: 35, y: 37, width: 30, height: 2 },
                { x: 35, y: 67, width: 30, height: 2 },
                { x: 35, y: 97, width: 30, height: 2 }
            ];

            traces.forEach(trace => {
                const element = document.createElement('div');
                element.className = 'trace horizontal';
                element.style.left = trace.x + '%';
                element.style.top = trace.y + '%';
                element.style.width = trace.width + 'px';
                element.style.height = trace.height + 'px';
                board.appendChild(element);
            });
        }

        function addVias() {
            const board = document.getElementById('pcbBoard');

            const vias = [
                { x: 50, y: 40 },
                { x: 50, y: 70 },
                { x: 50, y: 100 },
                { x: 75, y: 40 },
                { x: 75, y: 70 }
            ];

            vias.forEach(via => {
                const element = document.createElement('div');
                element.className = 'via';
                element.style.left = via.x + '%';
                element.style.top = via.y + '%';
                board.appendChild(element);
            });
        }

        function showLayer(layer) {
            currentLayer = layer;

            // Update button states
            document.querySelectorAll('.layer-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update layer info
            const layerInfo = {
                top: 'Top Layer: Component placement and top copper traces',
                bottom: 'Bottom Layer: Bottom copper traces and ground plane',
                silkscreen: 'Silkscreen: Component labels and reference designators',
                soldermask: 'Solder Mask: Protective coating over copper traces',
                drill: 'Drill Holes: Through-holes and vias for component mounting'
            };

            document.getElementById('layerInfo').innerHTML = `<strong>${layer.charAt(0).toUpperCase() + layer.slice(1)} Layer:</strong> ${layerInfo[layer]}`;

            // Update PCB display based on layer
            updatePCBDisplay(layer);
        }

        function updatePCBDisplay(layer) {
            const board = document.getElementById('pcbBoard');

            switch(layer) {
                case 'top':
                    board.style.background = '#1a5f1a';
                    document.querySelectorAll('.component').forEach(comp => {
                        comp.style.display = 'flex';
                    });
                    document.querySelectorAll('.trace').forEach(trace => {
                        trace.style.display = 'block';
                    });
                    break;
                case 'bottom':
                    board.style.background = '#1a3f5f';
                    document.querySelectorAll('.component').forEach(comp => {
                        comp.style.display = 'none';
                    });
                    break;
                case 'silkscreen':
                    board.style.background = '#f8f9fa';
                    document.querySelectorAll('.component').forEach(comp => {
                        comp.style.background = 'transparent';
                        comp.style.border = '2px solid #333';
                        comp.style.color = '#333';
                    });
                    break;
                case 'soldermask':
                    board.style.background = '#2d5016';
                    break;
                case 'drill':
                    board.style.background = '#333';
                    document.querySelectorAll('.component').forEach(comp => {
                        comp.style.display = 'none';
                    });
                    document.querySelectorAll('.trace').forEach(trace => {
                        trace.style.display = 'none';
                    });
                    break;
            }
        }

        function populateBOM() {
            const tbody = document.querySelector('#bomTable tbody');
            tbody.innerHTML = '';

            bomData.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${item.designator}</td>
                    <td>${item.component}</td>
                    <td>${item.value}</td>
                    <td>${item.package}</td>
                    <td>${item.quantity}</td>
                    <td>${item.manufacturer}</td>
                    <td>${item.partNumber}</td>
                    <td>${item.unitCost}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function populateComponentDetails() {
            const grid = document.getElementById('componentDetailsGrid');

            if (window.ECGData && window.ECGData.components) {
                const components = window.ECGData.components;

                // Instrumentation Amplifier
                const inaCard = createDetailCard('INA128UA Instrumentation Amplifier', components.instrumentation_amplifier.specifications);
                grid.appendChild(inaCard);

                // Operational Amplifiers
                const opaCard = createDetailCard('OPA2131UA Op-Amp', components.operational_amplifiers.opa2131.specifications);
                grid.appendChild(opaCard);

                // Power Supply
                const powerCard = createDetailCard('DC-DC Converter', components.power_supply.specifications);
                grid.appendChild(powerCard);

                // Microcontroller
                const mcuCard = createDetailCard('STM32F4 Microcontroller', components.microcontroller.specifications);
                grid.appendChild(mcuCard);
            }
        }

        function createDetailCard(title, specs) {
            const card = document.createElement('div');
            card.className = 'detail-card';

            let specsHtml = '';
            for (const [key, value] of Object.entries(specs)) {
                specsHtml += `
                    <tr>
                        <td>${key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}:</td>
                        <td>${value}</td>
                    </tr>
                `;
            }

            card.innerHTML = `
                <h4>${title}</h4>
                <table class="spec-table">
                    ${specsHtml}
                </table>
            `;

            return card;
        }

        function showComponentInfo(component) {
            const modal = document.getElementById('componentModal');
            const modalBody = document.getElementById('modalBody');

            modalBody.innerHTML = `
                <h2>${component.name} (${component.id})</h2>
                <p><strong>Type:</strong> ${component.type.charAt(0).toUpperCase() + component.type.slice(1)}</p>
                <p><strong>Position:</strong> X: ${component.x}%, Y: ${component.y}%</p>
                <p><strong>Size:</strong> ${component.width}px × ${component.height}px</p>
                <p>Click on the component in the PCB layout to see detailed specifications.</p>
            `;

            modal.style.display = 'block';
        }

        function zoomIn() {
            zoomLevel = Math.min(zoomLevel * 1.2, 3);
            applyZoom();
        }

        function zoomOut() {
            zoomLevel = Math.max(zoomLevel / 1.2, 0.5);
            applyZoom();
        }

        function resetZoom() {
            zoomLevel = 1;
            applyZoom();
        }

        function applyZoom() {
            const board = document.getElementById('pcbBoard');
            board.style.transform = `scale(${zoomLevel})`;
        }

        function exportGerber() {
            alert('Gerber export functionality would generate manufacturing files including:\n\n' +
                  '• Copper layers (GTL, GBL)\n' +
                  '• Solder mask (GTS, GBS)\n' +
                  '• Silkscreen (GTO)\n' +
                  '• Drill files (DRL)\n' +
                  '• Pick and place (CSV)');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializePCBViewer);

        // Modal close functionality
        document.querySelector('.close').addEventListener('click', function() {
            document.getElementById('componentModal').style.display = 'none';
        });

        window.addEventListener('click', function(e) {
            const modal = document.getElementById('componentModal');
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    </script>
</body>
</html>
