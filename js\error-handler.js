// Global Error Handler for ECG Signal System

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 50;
        this.setupErrorHandling();
    }
    
    setupErrorHandling() {
        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString()
            });
        });
        
        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError({
                    type: 'Resource Loading Error',
                    message: `Failed to load: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }
    
    logError(error) {
        // Add to error log
        this.errors.push(error);
        
        // Limit error log size
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }
        
        // Log to console
        console.error('ECG System Error:', error);
        
        // Show user-friendly notification for critical errors
        if (this.isCriticalError(error)) {
            this.showErrorNotification(error);
        }
    }
    
    isCriticalError(error) {
        const criticalPatterns = [
            /plotly/i,
            /cannot read property/i,
            /undefined is not a function/i,
            /network error/i,
            /failed to fetch/i
        ];
        
        return criticalPatterns.some(pattern => 
            pattern.test(error.message) || 
            (error.stack && pattern.test(error.stack))
        );
    }
    
    showErrorNotification(error) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="error-message">
                    <h4>System Notice</h4>
                    <p>${this.getUserFriendlyMessage(error)}</p>
                </div>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;
        
        // Add styles for notification content
        const style = document.createElement('style');
        style.textContent = `
            .error-content {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                gap: 0.75rem;
            }
            .error-icon {
                color: #856404;
                font-size: 1.2rem;
                margin-top: 0.2rem;
            }
            .error-message h4 {
                margin: 0 0 0.5rem 0;
                color: #856404;
                font-size: 1rem;
            }
            .error-message p {
                margin: 0;
                color: #856404;
                font-size: 0.9rem;
                line-height: 1.4;
            }
            .error-close {
                background: none;
                border: none;
                color: #856404;
                cursor: pointer;
                font-size: 1rem;
                padding: 0.25rem;
                border-radius: 4px;
                margin-left: auto;
            }
            .error-close:hover {
                background: rgba(133, 100, 4, 0.1);
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        
        if (!document.querySelector('#error-notification-styles')) {
            style.id = 'error-notification-styles';
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }
    
    getUserFriendlyMessage(error) {
        if (error.message.toLowerCase().includes('plotly')) {
            return 'Some chart features may not be available. The system will continue to work with basic functionality.';
        }
        
        if (error.type === 'Resource Loading Error') {
            return 'Some resources failed to load. Please check your internet connection and refresh the page.';
        }
        
        if (error.message.toLowerCase().includes('network')) {
            return 'Network connectivity issue detected. Some features may be limited.';
        }
        
        return 'A minor system issue was detected. The application should continue to function normally.';
    }
    
    getErrorReport() {
        return {
            totalErrors: this.errors.length,
            errors: this.errors,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }
    
    clearErrors() {
        this.errors = [];
    }
    
    // Check for common issues and provide solutions
    performSystemCheck() {
        const issues = [];
        
        // Check if required libraries are loaded
        if (typeof Plotly === 'undefined') {
            issues.push({
                type: 'Missing Library',
                message: 'Plotly.js library not loaded',
                solution: 'Chart features will use fallback displays'
            });
        }
        
        // Check if required DOM elements exist
        const requiredElements = ['ecg-preview', 'componentModal'];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                issues.push({
                    type: 'Missing Element',
                    message: `Required element '${id}' not found`,
                    solution: 'Some features may not be available'
                });
            }
        });
        
        // Check browser compatibility
        if (!window.fetch) {
            issues.push({
                type: 'Browser Compatibility',
                message: 'Fetch API not supported',
                solution: 'Please use a modern browser'
            });
        }
        
        if (issues.length > 0) {
            console.warn('System Check Issues:', issues);
        } else {
            console.log('System Check: All systems operational');
        }
        
        return issues;
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Perform system check after page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        errorHandler.performSystemCheck();
    }, 1000);
});

// Export for global access
window.errorHandler = errorHandler;

// Add helpful console messages
console.log('%c🫀 ECG Signal System Loaded', 'color: #ff6b6b; font-size: 16px; font-weight: bold;');
console.log('%cSystem Status: Initializing...', 'color: #667eea; font-size: 12px;');

// Check for development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('%c🔧 Development Mode Active', 'color: #ffa500; font-size: 12px;');
    console.log('Error reporting enabled. Check errorHandler.getErrorReport() for details.');
}
