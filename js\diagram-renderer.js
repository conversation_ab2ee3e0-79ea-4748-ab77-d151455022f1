// Interactive Diagram Renderer for ECG Signal System

class DiagramRenderer {
    constructor() {
        this.currentDiagram = 'block';
        this.animationEnabled = false;
        this.zoomLevel = 1.0;
        this.selectedComponent = null;
        
        this.blockComponents = [
            {
                id: 'electrodes',
                title: 'ECG Electrodes',
                description: 'Ag/AgCl Electrodes\n±5mV Input',
                icon: 'fas fa-circle',
                type: 'input',
                x: 50,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'protection',
                title: 'Input Protection',
                description: 'ESD Protection\nOvervoltage Clamp',
                icon: 'fas fa-shield-alt',
                type: 'input',
                x: 250,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'ina128',
                title: 'INA128UA',
                description: 'Instrumentation Amp\nGain: 1100x',
                icon: 'fas fa-expand-arrows-alt',
                type: 'processing',
                x: 450,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'filters',
                title: 'Analog Filters',
                description: 'HPF: 0.5Hz\nLPF: 150Hz\nNotch: 50Hz',
                icon: 'fas fa-filter',
                type: 'processing',
                x: 650,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'buffer',
                title: 'Output Buffer',
                description: 'OP07D\nLevel Shifter',
                icon: 'fas fa-wave-square',
                type: 'processing',
                x: 850,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'adc',
                title: 'STM32F4 ADC',
                description: '12-bit ADC\n256 Hz Sampling',
                icon: 'fas fa-microchip',
                type: 'output',
                x: 1050,
                y: 200,
                width: 150,
                height: 100
            },
            {
                id: 'power',
                title: 'Power Supply',
                description: 'A0509S-1WR3\n±9V Output',
                icon: 'fas fa-bolt',
                type: 'power',
                x: 550,
                y: 50,
                width: 150,
                height: 80
            }
        ];
        
        this.connections = [
            { from: 'electrodes', to: 'protection' },
            { from: 'protection', to: 'ina128' },
            { from: 'ina128', to: 'filters' },
            { from: 'filters', to: 'buffer' },
            { from: 'buffer', to: 'adc' },
            { from: 'power', to: 'ina128' },
            { from: 'power', to: 'filters' },
            { from: 'power', to: 'buffer' }
        ];
        
        this.schematicComponents = [
            {
                id: 'U1',
                type: 'IC',
                symbol: 'INA128UA',
                value: 'INA128UA/2K5',
                x: 400,
                y: 250,
                pins: 8,
                package: 'SOIC-8'
            },
            {
                id: 'U2',
                type: 'IC',
                symbol: 'OPA2131UA',
                value: 'OPA2131UA/2K5',
                x: 600,
                y: 250,
                pins: 8,
                package: 'SOIC-8'
            },
            {
                id: 'R1',
                type: 'RESISTOR',
                symbol: 'R',
                value: '1kΩ',
                x: 300,
                y: 200,
                rotation: 0
            },
            {
                id: 'R2',
                type: 'RESISTOR',
                symbol: 'R',
                value: '1kΩ',
                x: 300,
                y: 300,
                rotation: 0
            },
            {
                id: 'C1',
                type: 'CAPACITOR',
                symbol: 'C',
                value: '1μF',
                x: 500,
                y: 200,
                rotation: 0
            },
            {
                id: 'C2',
                type: 'CAPACITOR',
                symbol: 'C',
                value: '1μF',
                x: 700,
                y: 200,
                rotation: 0
            }
        ];
        
        this.initializeRenderer();
    }
    
    initializeRenderer() {
        this.renderBlockDiagram();
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Component click handlers will be added dynamically
        document.addEventListener('click', this.handleComponentClick.bind(this));
    }
    
    renderBlockDiagram() {
        const canvas = document.getElementById('blockCanvas');
        canvas.innerHTML = '';
        
        // Render connections first (behind components)
        this.renderConnections(canvas);
        
        // Render components
        this.blockComponents.forEach(component => {
            this.renderBlockComponent(canvas, component);
        });
        
        // Add signal flow animation if enabled
        if (this.animationEnabled) {
            this.startSignalAnimation();
        }
    }
    
    renderBlockComponent(canvas, component) {
        const block = document.createElement('div');
        block.className = `block ${component.type}`;
        block.id = component.id;
        block.style.left = component.x + 'px';
        block.style.top = component.y + 'px';
        block.style.width = component.width + 'px';
        block.style.height = component.height + 'px';
        
        block.innerHTML = `
            <i class="${component.icon} block-icon"></i>
            <div class="block-title">${component.title}</div>
            <div class="block-description">${component.description}</div>
        `;
        
        block.addEventListener('click', () => this.showComponentInfo(component));
        block.addEventListener('mouseenter', () => this.highlightComponent(component.id));
        block.addEventListener('mouseleave', () => this.unhighlightComponent(component.id));
        
        canvas.appendChild(block);
    }
    
    renderConnections(canvas) {
        this.connections.forEach(connection => {
            const fromComponent = this.blockComponents.find(c => c.id === connection.from);
            const toComponent = this.blockComponents.find(c => c.id === connection.to);
            
            if (fromComponent && toComponent) {
                this.drawConnection(canvas, fromComponent, toComponent);
            }
        });
    }
    
    drawConnection(canvas, from, to) {
        const line = document.createElement('div');
        line.className = 'connection-line';
        
        // Calculate connection points
        const fromX = from.x + from.width;
        const fromY = from.y + from.height / 2;
        const toX = to.x;
        const toY = to.y + to.height / 2;
        
        // Handle different connection types
        if (from.type === 'power') {
            // Vertical connection from power supply
            this.drawVerticalConnection(canvas, from, to);
        } else {
            // Horizontal connection
            this.drawHorizontalConnection(canvas, fromX, fromY, toX, toY);
        }
    }
    
    drawHorizontalConnection(canvas, fromX, fromY, toX, toY) {
        const line = document.createElement('div');
        line.className = 'connection-line';
        
        const width = toX - fromX;
        const height = 3;
        
        line.style.left = fromX + 'px';
        line.style.top = (fromY - height/2) + 'px';
        line.style.width = width + 'px';
        line.style.height = height + 'px';
        
        canvas.appendChild(line);
        
        // Add arrow
        const arrow = document.createElement('div');
        arrow.className = 'connection-arrow right';
        arrow.style.left = (toX - 10) + 'px';
        arrow.style.top = (fromY - 5) + 'px';
        
        canvas.appendChild(arrow);
    }
    
    drawVerticalConnection(canvas, from, to) {
        const line = document.createElement('div');
        line.className = 'connection-line';
        
        const fromX = from.x + from.width / 2;
        const fromY = from.y + from.height;
        const toX = to.x + to.width / 2;
        const toY = to.y;
        
        // Vertical line
        line.style.left = (fromX - 1.5) + 'px';
        line.style.top = fromY + 'px';
        line.style.width = '3px';
        line.style.height = (toY - fromY) + 'px';
        
        canvas.appendChild(line);
        
        // Add arrow
        const arrow = document.createElement('div');
        arrow.className = 'connection-arrow down';
        arrow.style.left = (toX - 5) + 'px';
        arrow.style.top = (toY - 10) + 'px';
        
        canvas.appendChild(arrow);
    }
    
    renderSchematicDiagram() {
        const canvas = document.getElementById('schematicCanvas');
        canvas.innerHTML = '';
        
        // Create SVG container
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('width', '100%');
        svg.setAttribute('height', '600');
        svg.setAttribute('viewBox', '0 0 1200 600');
        
        // Render schematic components
        this.schematicComponents.forEach(component => {
            this.renderSchematicComponent(svg, component);
        });
        
        // Render connections
        this.renderSchematicConnections(svg);
        
        canvas.appendChild(svg);
    }
    
    renderSchematicComponent(svg, component) {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.setAttribute('class', 'component');
        group.setAttribute('id', component.id);
        group.setAttribute('transform', `translate(${component.x}, ${component.y})`);
        
        switch (component.type) {
            case 'IC':
                this.drawIC(group, component);
                break;
            case 'RESISTOR':
                this.drawResistor(group, component);
                break;
            case 'CAPACITOR':
                this.drawCapacitor(group, component);
                break;
        }
        
        // Add component label
        const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        label.setAttribute('x', '0');
        label.setAttribute('y', '-10');
        label.setAttribute('text-anchor', 'middle');
        label.setAttribute('font-size', '12');
        label.setAttribute('font-weight', 'bold');
        label.setAttribute('fill', '#333');
        label.textContent = component.id;
        group.appendChild(label);
        
        // Add component value
        const value = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        value.setAttribute('x', '0');
        value.setAttribute('y', '35');
        value.setAttribute('text-anchor', 'middle');
        value.setAttribute('font-size', '10');
        value.setAttribute('fill', '#666');
        value.textContent = component.value;
        group.appendChild(value);
        
        group.addEventListener('click', () => this.showComponentInfo(component));
        
        svg.appendChild(group);
    }
    
    drawIC(group, component) {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('x', '-30');
        rect.setAttribute('y', '-15');
        rect.setAttribute('width', '60');
        rect.setAttribute('height', '30');
        rect.setAttribute('fill', 'white');
        rect.setAttribute('stroke', '#333');
        rect.setAttribute('stroke-width', '2');
        rect.setAttribute('rx', '3');
        group.appendChild(rect);
        
        // Add pins
        for (let i = 0; i < component.pins / 2; i++) {
            // Left pins
            const leftPin = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            leftPin.setAttribute('x1', '-40');
            leftPin.setAttribute('y1', -10 + i * 10);
            leftPin.setAttribute('x2', '-30');
            leftPin.setAttribute('y2', -10 + i * 10);
            leftPin.setAttribute('stroke', '#333');
            leftPin.setAttribute('stroke-width', '2');
            group.appendChild(leftPin);
            
            // Right pins
            const rightPin = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            rightPin.setAttribute('x1', '30');
            rightPin.setAttribute('y1', -10 + i * 10);
            rightPin.setAttribute('x2', '40');
            rightPin.setAttribute('y2', -10 + i * 10);
            rightPin.setAttribute('stroke', '#333');
            rightPin.setAttribute('stroke-width', '2');
            group.appendChild(rightPin);
        }
        
        // Add IC symbol text
        const symbol = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        symbol.setAttribute('x', '0');
        symbol.setAttribute('y', '3');
        symbol.setAttribute('text-anchor', 'middle');
        symbol.setAttribute('font-size', '8');
        symbol.setAttribute('font-weight', 'bold');
        symbol.setAttribute('fill', '#333');
        symbol.textContent = component.symbol;
        group.appendChild(symbol);
    }
    
    drawResistor(group, component) {
        // Resistor zigzag pattern
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        path.setAttribute('d', 'M-20,0 L-15,0 L-12,-5 L-8,5 L-4,-5 L0,5 L4,-5 L8,5 L12,-5 L15,0 L20,0');
        path.setAttribute('fill', 'none');
        path.setAttribute('stroke', '#333');
        path.setAttribute('stroke-width', '2');
        group.appendChild(path);
        
        // Connection lines
        const leftLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        leftLine.setAttribute('x1', '-30');
        leftLine.setAttribute('y1', '0');
        leftLine.setAttribute('x2', '-20');
        leftLine.setAttribute('y2', '0');
        leftLine.setAttribute('stroke', '#333');
        leftLine.setAttribute('stroke-width', '2');
        group.appendChild(leftLine);
        
        const rightLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        rightLine.setAttribute('x1', '20');
        rightLine.setAttribute('y1', '0');
        rightLine.setAttribute('x2', '30');
        rightLine.setAttribute('y2', '0');
        rightLine.setAttribute('stroke', '#333');
        rightLine.setAttribute('stroke-width', '2');
        group.appendChild(rightLine);
    }
    
    drawCapacitor(group, component) {
        // Capacitor plates
        const leftPlate = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        leftPlate.setAttribute('x1', '-3');
        leftPlate.setAttribute('y1', '-10');
        leftPlate.setAttribute('x2', '-3');
        leftPlate.setAttribute('y2', '10');
        leftPlate.setAttribute('stroke', '#333');
        leftPlate.setAttribute('stroke-width', '3');
        group.appendChild(leftPlate);
        
        const rightPlate = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        rightPlate.setAttribute('x1', '3');
        rightPlate.setAttribute('y1', '-10');
        rightPlate.setAttribute('x2', '3');
        rightPlate.setAttribute('y2', '10');
        rightPlate.setAttribute('stroke', '#333');
        rightPlate.setAttribute('stroke-width', '3');
        group.appendChild(rightPlate);
        
        // Connection lines
        const leftLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        leftLine.setAttribute('x1', '-30');
        leftLine.setAttribute('y1', '0');
        leftLine.setAttribute('x2', '-3');
        leftLine.setAttribute('y2', '0');
        leftLine.setAttribute('stroke', '#333');
        leftLine.setAttribute('stroke-width', '2');
        group.appendChild(leftLine);
        
        const rightLine = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        rightLine.setAttribute('x1', '3');
        rightLine.setAttribute('y1', '0');
        rightLine.setAttribute('x2', '30');
        rightLine.setAttribute('y2', '0');
        rightLine.setAttribute('stroke', '#333');
        rightLine.setAttribute('stroke-width', '2');
        group.appendChild(rightLine);
    }
    
    renderSchematicConnections(svg) {
        // Define connections between components
        const connections = [
            { from: 'R1', to: 'U1', fromPin: 'right', toPin: 'left1' },
            { from: 'R2', to: 'U1', fromPin: 'right', toPin: 'left2' },
            { from: 'U1', to: 'C1', fromPin: 'right1', toPin: 'left' },
            { from: 'C1', to: 'U2', fromPin: 'right', toPin: 'left1' },
            { from: 'U2', to: 'C2', fromPin: 'right1', toPin: 'left' }
        ];
        
        connections.forEach(conn => {
            this.drawSchematicConnection(svg, conn);
        });
    }
    
    drawSchematicConnection(svg, connection) {
        const fromComp = this.schematicComponents.find(c => c.id === connection.from);
        const toComp = this.schematicComponents.find(c => c.id === connection.to);
        
        if (fromComp && toComp) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('class', 'wire');
            line.setAttribute('x1', fromComp.x + 30);
            line.setAttribute('y1', fromComp.y);
            line.setAttribute('x2', toComp.x - 30);
            line.setAttribute('y2', toComp.y);
            
            svg.appendChild(line);
        }
    }
    
    renderSignalFlowDiagram() {
        const canvas = document.getElementById('signalFlowCanvas');
        canvas.innerHTML = '';
        
        // Create signal flow visualization with Plotly
        this.createSignalFlowPlots(canvas);
    }
    
    createSignalFlowPlots(container) {
        // Create multiple subplot areas for different stages
        const stages = [
            { id: 'input', title: 'Input Signal', color: '#4CAF50' },
            { id: 'amplified', title: 'After Amplification', color: '#2196F3' },
            { id: 'filtered', title: 'After Filtering', color: '#FF9800' },
            { id: 'output', title: 'Digital Output', color: '#9C27B0' }
        ];
        
        stages.forEach((stage, index) => {
            const plotDiv = document.createElement('div');
            plotDiv.id = `plot-${stage.id}`;
            plotDiv.style.width = '48%';
            plotDiv.style.height = '250px';
            plotDiv.style.display = 'inline-block';
            plotDiv.style.margin = '1%';
            
            container.appendChild(plotDiv);
            
            this.createStagePlot(plotDiv.id, stage, index);
        });
    }
    
    createStagePlot(containerId, stage, stageIndex) {
        // Generate sample ECG data for this stage
        const sampleRate = 256;
        const duration = 2; // 2 seconds
        const samples = sampleRate * duration;
        const time = Array.from({length: samples}, (_, i) => i / sampleRate);
        
        // Generate ECG signal with stage-specific modifications
        const data = this.generateStageSignal(samples, stageIndex);
        
        const trace = {
            x: time,
            y: data,
            type: 'scatter',
            mode: 'lines',
            line: { color: stage.color, width: 2 },
            name: stage.title
        };
        
        const layout = {
            title: { text: stage.title, font: { size: 14 } },
            xaxis: { title: 'Time (s)', showgrid: true },
            yaxis: { title: 'Amplitude', showgrid: true },
            margin: { t: 40, r: 20, b: 40, l: 50 },
            showlegend: false
        };
        
        const config = { displayModeBar: false, responsive: true };
        
        Plotly.newPlot(containerId, [trace], layout, config);
    }
    
    generateStageSignal(samples, stage) {
        const data = [];
        const heartRate = parseFloat(document.getElementById('heartRate')?.value || 72);
        const amplitude = parseFloat(document.getElementById('amplitude')?.value || 1.0);
        const noiseLevel = parseFloat(document.getElementById('noiseLevel')?.value || 0.05);
        
        for (let i = 0; i < samples; i++) {
            const t = i / 256;
            const beatPhase = (t * heartRate / 60) % 1;
            
            let signal = this.generateECGBeat(beatPhase) * amplitude;
            
            // Apply stage-specific modifications
            switch (stage) {
                case 0: // Input
                    signal += (Math.random() - 0.5) * noiseLevel;
                    break;
                case 1: // Amplified
                    signal *= 1100; // INA128UA gain
                    signal += (Math.random() - 0.5) * noiseLevel * 1100;
                    break;
                case 2: // Filtered
                    signal *= 1100;
                    // Reduced noise after filtering
                    signal += (Math.random() - 0.5) * noiseLevel * 100;
                    break;
                case 3: // Digital
                    signal *= 1100;
                    // Quantized to 12-bit ADC
                    signal = Math.round(signal * 4096) / 4096;
                    break;
            }
            
            data.push(signal);
        }
        
        return data;
    }
    
    generateECGBeat(phase) {
        let amplitude = 0;
        
        // P wave
        if (phase >= 0.0 && phase <= 0.15) {
            const pPhase = (phase - 0.0) / 0.15;
            amplitude += 0.2 * Math.exp(-Math.pow((pPhase - 0.5) / 0.3, 2));
        }
        
        // QRS complex
        if (phase >= 0.2 && phase <= 0.35) {
            const qrsPhase = (phase - 0.2) / 0.15;
            if (qrsPhase <= 0.2) {
                amplitude -= 0.2 * (qrsPhase / 0.2);
            } else if (qrsPhase <= 0.6) {
                const rPhase = (qrsPhase - 0.2) / 0.4;
                amplitude += Math.sin(rPhase * Math.PI);
            } else {
                const sPhase = (qrsPhase - 0.6) / 0.4;
                amplitude -= 0.3 * Math.sin(sPhase * Math.PI);
            }
        }
        
        // T wave
        if (phase >= 0.5 && phase <= 0.8) {
            const tPhase = (phase - 0.5) / 0.3;
            amplitude += 0.3 * Math.exp(-Math.pow((tPhase - 0.5) / 0.4, 2));
        }
        
        return amplitude;
    }
    
    showComponentInfo(component) {
        const panel = document.getElementById('infoPanel');
        const title = document.getElementById('infoTitle');
        const content = document.getElementById('infoContent');
        
        title.textContent = component.title || component.id;
        content.innerHTML = this.getComponentDetails(component);
        
        panel.classList.add('open');
        this.selectedComponent = component;
    }
    
    getComponentDetails(component) {
        // Get detailed component information
        const details = {
            'electrodes': `
                <h4>ECG Electrodes (Ag/AgCl)</h4>
                <p><strong>Function:</strong> Convert ionic current to electronic current</p>
                <ul>
                    <li><strong>Material:</strong> Silver/Silver Chloride</li>
                    <li><strong>Input Range:</strong> ±5mV</li>
                    <li><strong>Impedance:</strong> <2kΩ @ 10Hz</li>
                    <li><strong>Offset Voltage:</strong> <100μV</li>
                    <li><strong>Configuration:</strong> 3-lead (RA, LA, RL)</li>
                </ul>
            `,
            'ina128': `
                <h4>INA128UA Instrumentation Amplifier</h4>
                <p><strong>Function:</strong> High-precision differential amplification</p>
                <ul>
                    <li><strong>Gain:</strong> 1100x (60.8dB)</li>
                    <li><strong>CMRR:</strong> 120dB min @ DC</li>
                    <li><strong>Input Offset:</strong> 50μV max</li>
                    <li><strong>Bandwidth:</strong> 200kHz</li>
                    <li><strong>Package:</strong> SOIC-8</li>
                </ul>
            `,
            'filters': `
                <h4>Analog Filter Stages</h4>
                <p><strong>Function:</strong> Signal conditioning and noise reduction</p>
                <ul>
                    <li><strong>High-pass:</strong> 0.5Hz (-3dB)</li>
                    <li><strong>Low-pass:</strong> 150Hz (-3dB)</li>
                    <li><strong>Notch:</strong> 50/60Hz rejection</li>
                    <li><strong>Implementation:</strong> Active filters with OPA2131UA</li>
                </ul>
            `
        };
        
        return details[component.id] || `
            <h4>${component.title || component.id}</h4>
            <p>Component information not available.</p>
        `;
    }
    
    highlightComponent(componentId) {
        const element = document.getElementById(componentId);
        if (element) {
            element.classList.add('highlighted');
        }
    }
    
    unhighlightComponent(componentId) {
        const element = document.getElementById(componentId);
        if (element) {
            element.classList.remove('highlighted');
        }
    }
    
    startSignalAnimation() {
        const connections = document.querySelectorAll('.connection-line');
        connections.forEach(line => {
            line.classList.add('animated');
        });
    }
    
    stopSignalAnimation() {
        const connections = document.querySelectorAll('.connection-line');
        connections.forEach(line => {
            line.classList.remove('animated');
        });
    }
    
    handleComponentClick(event) {
        if (event.target.closest('.component') || event.target.closest('.block')) {
            // Component click handled by individual handlers
            return;
        }
        
        // Clicked outside components - close info panel
        this.closeInfoPanel();
    }
    
    closeInfoPanel() {
        const panel = document.getElementById('infoPanel');
        panel.classList.remove('open');
        this.selectedComponent = null;
    }
}

// Global diagram renderer instance
let diagramRenderer;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    diagramRenderer = new DiagramRenderer();
});

// Export for global access
window.DiagramRenderer = DiagramRenderer;
