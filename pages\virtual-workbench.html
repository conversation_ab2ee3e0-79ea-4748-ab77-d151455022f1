<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Simulator Workbench - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/workbench.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="pcb-viewer.html" class="nav-link">PCB Viewer</a>
                </li>
                <li class="nav-item">
                    <a href="#workbench" class="nav-link active">Virtual Workbench</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Workbench Container -->
    <div class="workbench-container">
        <!-- Top Toolbar -->
        <div class="workbench-toolbar">
            <div class="toolbar-section">
                <div class="dropdown">
                    <button type="button" class="toolbar-btn dropdown-toggle" onclick="toggleDropdown('fileMenu')">
                        <i class="fas fa-file"></i> File
                    </button>
                    <div class="dropdown-menu" id="fileMenu">
                        <a href="#" onclick="newProject()"><i class="fas fa-plus"></i> New Project</a>
                        <a href="#" onclick="openProject()"><i class="fas fa-folder-open"></i> Open Project</a>
                        <a href="#" onclick="saveProject()"><i class="fas fa-save"></i> Save Project</a>
                        <a href="#" onclick="exportProject()"><i class="fas fa-download"></i> Export</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <button type="button" class="toolbar-btn dropdown-toggle" onclick="toggleDropdown('viewMenu')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <div class="dropdown-menu" id="viewMenu">
                        <a href="#" onclick="switchView('schematic')"><i class="fas fa-project-diagram"></i> Schematic View</a>
                        <a href="#" onclick="switchView('pcb')"><i class="fas fa-microchip"></i> PCB Layout</a>
                        <a href="#" onclick="switchView('3d')"><i class="fas fa-cube"></i> 3D View</a>
                        <a href="#" onclick="toggleGrid()"><i class="fas fa-th"></i> Toggle Grid</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <button type="button" class="toolbar-btn dropdown-toggle" onclick="toggleDropdown('simulateMenu')">
                        <i class="fas fa-play"></i> Simulate
                    </button>
                    <div class="dropdown-menu" id="simulateMenu">
                        <a href="#" onclick="runSimulation()"><i class="fas fa-play"></i> Run Simulation</a>
                        <a href="#" onclick="stopSimulation()"><i class="fas fa-stop"></i> Stop Simulation</a>
                        <a href="#" onclick="showWaveforms()"><i class="fas fa-wave-square"></i> View Waveforms</a>
                        <a href="#" onclick="analyzeFrequency()"><i class="fas fa-chart-line"></i> Frequency Analysis</a>
                    </div>
                </div>
                
                <div class="dropdown">
                    <button type="button" class="toolbar-btn dropdown-toggle" onclick="toggleDropdown('toolsMenu')">
                        <i class="fas fa-tools"></i> Tools
                    </button>
                    <div class="dropdown-menu" id="toolsMenu">
                        <a href="#" onclick="selectTool('wire')"><i class="fas fa-minus"></i> Wire Tool</a>
                        <a href="#" onclick="selectTool('move')"><i class="fas fa-arrows-alt"></i> Move Tool</a>
                        <a href="#" onclick="selectTool('rotate')"><i class="fas fa-redo"></i> Rotate Tool</a>
                        <a href="#" onclick="selectTool('delete')"><i class="fas fa-trash"></i> Delete Tool</a>
                    </div>
                </div>
            </div>
            
            <div class="toolbar-section">
                <button type="button" class="toolbar-btn" onclick="zoomIn()" title="Zoom In">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button type="button" class="toolbar-btn" onclick="zoomOut()" title="Zoom Out">
                    <i class="fas fa-search-minus"></i>
                </button>
                <button type="button" class="toolbar-btn" onclick="fitToScreen()" title="Fit to Screen">
                    <i class="fas fa-expand"></i>
                </button>
                <span class="zoom-level" id="zoomLevel">100%</span>
            </div>
        </div>

        <!-- Main Workbench Area -->
        <div class="workbench-main">
            <!-- Component Library Sidebar -->
            <div class="component-library">
                <div class="library-header">
                    <h3><i class="fas fa-microchip"></i> Component Library</h3>
                    <div class="library-search">
                        <input type="text" placeholder="Search components..." id="componentSearch">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                
                <div class="library-categories">
                    <div class="category active" onclick="showCategory('amplifiers')">
                        <i class="fas fa-expand-arrows-alt"></i> Amplifiers
                    </div>
                    <div class="category" onclick="showCategory('passives')">
                        <i class="fas fa-circle"></i> Passive Components
                    </div>
                    <div class="category" onclick="showCategory('power')">
                        <i class="fas fa-bolt"></i> Power Supply
                    </div>
                    <div class="category" onclick="showCategory('connectors')">
                        <i class="fas fa-plug"></i> Connectors
                    </div>
                    <div class="category" onclick="showCategory('digital')">
                        <i class="fas fa-microchip"></i> Digital ICs
                    </div>
                </div>
                
                <div class="component-list" id="componentList">
                    <!-- Components will be populated here -->
                </div>
            </div>

            <!-- Design Canvas -->
            <div class="design-canvas-container">
                <div class="canvas-header">
                    <div class="canvas-tabs">
                        <div class="tab active" onclick="switchTab('schematic')">
                            <i class="fas fa-project-diagram"></i> Schematic
                        </div>
                        <div class="tab" onclick="switchTab('pcb')">
                            <i class="fas fa-memory"></i> PCB Layout
                        </div>
                        <div class="tab" onclick="switchTab('simulation')">
                            <i class="fas fa-wave-square"></i> Simulation
                        </div>
                    </div>
                    
                    <div class="canvas-controls">
                        <button type="button" class="control-btn" onclick="clearCanvas()" title="Clear Canvas">
                            <i class="fas fa-eraser"></i>
                        </button>
                        <button type="button" class="control-btn" onclick="undoAction()" title="Undo">
                            <i class="fas fa-undo"></i>
                        </button>
                        <button type="button" class="control-btn" onclick="redoAction()" title="Redo">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                </div>
                
                <div class="design-canvas" id="designCanvas">
                    <div class="canvas-grid" id="canvasGrid"></div>
                    <div class="canvas-content" id="canvasContent">
                        <!-- Design elements will be placed here -->
                    </div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="properties-panel">
                <div class="panel-header">
                    <h3><i class="fas fa-cog"></i> Properties</h3>
                </div>
                
                <div class="properties-content" id="propertiesContent">
                    <div class="property-section">
                        <h4>Component Properties</h4>
                        <div class="property-item">
                            <label>Name:</label>
                            <input type="text" id="componentName" placeholder="Component name">
                        </div>
                        <div class="property-item">
                            <label>Value:</label>
                            <input type="text" id="componentValue" placeholder="Component value">
                        </div>
                        <div class="property-item">
                            <label>Package:</label>
                            <select id="componentPackage">
                                <option value="">Select package</option>
                                <option value="SOIC-8">SOIC-8</option>
                                <option value="SOP-8">SOP-8</option>
                                <option value="R1206">R1206</option>
                                <option value="C1206">C1206</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="property-section">
                        <h4>Position & Rotation</h4>
                        <div class="property-item">
                            <label>X Position:</label>
                            <input type="number" id="positionX" placeholder="X coordinate">
                        </div>
                        <div class="property-item">
                            <label>Y Position:</label>
                            <input type="number" id="positionY" placeholder="Y coordinate">
                        </div>
                        <div class="property-item">
                            <label>Rotation:</label>
                            <select id="rotation">
                                <option value="0">0°</option>
                                <option value="90">90°</option>
                                <option value="180">180°</option>
                                <option value="270">270°</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Status Bar -->
        <div class="status-bar">
            <div class="status-section">
                <span class="status-item">
                    <i class="fas fa-mouse-pointer"></i>
                    <span id="cursorPosition">X: 0, Y: 0</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-layer-group"></i>
                    <span id="currentLayer">Top Layer</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-th"></i>
                    <span id="gridSize">Grid: 2.54mm</span>
                </span>
            </div>
            
            <div class="status-section">
                <span class="status-item">
                    <i class="fas fa-microchip"></i>
                    <span id="componentCount">Components: 0</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-project-diagram"></i>
                    <span id="connectionCount">Connections: 0</span>
                </span>
                <span class="status-item simulation-status">
                    <i class="fas fa-circle" id="simulationIndicator"></i>
                    <span id="simulationStatus">Ready</span>
                </span>
            </div>
        </div>
    </div>

    <!-- Component Preview Modal -->
    <div id="componentPreviewModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeComponentPreview()">&times;</span>
            <div class="component-preview-content">
                <h3 id="previewTitle">Component Preview</h3>
                <div class="preview-image" id="previewImage">
                    <!-- Component image/diagram will be shown here -->
                </div>
                <div class="preview-specs" id="previewSpecs">
                    <!-- Component specifications will be shown here -->
                </div>
                <div class="preview-actions">
                    <button type="button" class="btn btn-primary" onclick="addComponentToCanvas()">
                        <i class="fas fa-plus"></i> Add to Canvas
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="viewDatasheet()">
                        <i class="fas fa-file-pdf"></i> View Datasheet
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    <script src="../js/workbench.js"></script>
    <script src="../js/drag-drop.js"></script>
    <script src="../data/ecg-data.js"></script>
</body>
</html>
