// Circuit Simulator for ECG Signal System

class CircuitSimulator {
    constructor() {
        this.components = new Map();
        this.connections = [];
        this.simulationRunning = false;
        this.currentValues = new Map();

        // Initialize component models
        this.initializeComponents();
    }

    initializeComponents() {
        // INA128UA Instrumentation Amplifier
        this.components.set('INA128UA', {
            type: 'instrumentation_amplifier',
            gain: 1100,
            cmrr: 120, // dB
            inputOffsetVoltage: 50e-6, // V
            inputBiasCurrent: 5e-9, // A
            bandwidth: 200e3, // Hz
            supplyVoltage: { min: 2.25, max: 18 }, // V
            transferFunction: this.inaTransferFunction.bind(this)
        });

        // OPA2131UA Operational Amplifier
        this.components.set('OPA2131UA', {
            type: 'operational_amplifier',
            gain: 1, // Unity gain buffer
            bandwidth: 4e6, // Hz
            slewRate: 20e6, // V/s
            inputOffsetVoltage: 250e-6, // V
            transferFunction: this.opampTransferFunction.bind(this)
        });

        // LM358 Operational Amplifier
        this.components.set('LM358', {
            type: 'operational_amplifier',
            gain: 1,
            bandwidth: 1e6, // Hz
            slewRate: 0.5e6, // V/s
            inputOffsetVoltage: 3e-3, // V
            transferFunction: this.opampTransferFunction.bind(this)
        });

        // OP07D Precision Op-Amp
        this.components.set('OP07D', {
            type: 'operational_amplifier',
            gain: 1,
            bandwidth: 600e3, // Hz
            slewRate: 0.3e6, // V/s
            inputOffsetVoltage: 25e-6, // V
            transferFunction: this.opampTransferFunction.bind(this)
        });

        // Passive components
        this.components.set('RESISTOR', {
            type: 'resistor',
            transferFunction: this.resistorTransferFunction.bind(this)
        });

        this.components.set('CAPACITOR', {
            type: 'capacitor',
            transferFunction: this.capacitorTransferFunction.bind(this)
        });

        // Filter stages
        this.components.set('HIGH_PASS_FILTER', {
            type: 'filter',
            filterType: 'highpass',
            cutoffFrequency: 0.5, // Hz
            order: 2,
            transferFunction: this.filterTransferFunction.bind(this)
        });

        this.components.set('LOW_PASS_FILTER', {
            type: 'filter',
            filterType: 'lowpass',
            cutoffFrequency: 150, // Hz
            order: 4,
            transferFunction: this.filterTransferFunction.bind(this)
        });

        this.components.set('NOTCH_FILTER', {
            type: 'filter',
            filterType: 'notch',
            centerFrequency: 50, // Hz
            qFactor: 30,
            transferFunction: this.notchFilterTransferFunction.bind(this)
        });
    }

    // Transfer function for INA128UA
    inaTransferFunction(input, frequency) {
        const component = this.components.get('INA128UA');

        // Frequency response (simplified real-valued model)
        const bandwidth = component.bandwidth;
        const gain = component.gain;

        // Apply gain and frequency response
        let output = input * gain;

        // Simple frequency rolloff (first-order low-pass)
        if (frequency > bandwidth) {
            output *= bandwidth / frequency;
        }

        // Add offset voltage
        output += component.inputOffsetVoltage;

        return output;
    }

    // Transfer function for operational amplifiers
    opampTransferFunction(input, frequency) {
        // Generic op-amp model
        const gain = 1; // Assuming unity gain configuration
        let output = input * gain;

        // Add frequency-dependent effects if needed
        return output;
    }

    // Transfer function for resistors
    resistorTransferFunction(input, resistance) {
        // Ohm's law: V = I * R
        return input; // For voltage divider calculations
    }

    // Transfer function for capacitors
    capacitorTransferFunction(input, frequency, capacitance) {
        // Capacitive reactance: Xc = 1 / (2πfC)
        const reactance = 1 / (2 * Math.PI * frequency * capacitance);
        return input; // Simplified model
    }

    // Transfer function for filters
    filterTransferFunction(input, frequency) {
        // This would implement specific filter responses
        return input;
    }

    // Notch filter transfer function
    notchFilterTransferFunction(input, frequency) {
        const component = this.components.get('NOTCH_FILTER');
        const f0 = component.centerFrequency;
        const Q = component.qFactor;

        // Notch filter response
        const ratio = frequency / f0;
        const denominator = 1 + Math.pow(Q * (ratio - 1/ratio), 2);
        const attenuation = 1 / Math.sqrt(denominator);

        return input * attenuation;
    }

    // Simulate complete signal chain
    simulateSignalChain(inputSignal, frequency) {
        let signal = inputSignal;

        // Stage 1: Input protection (simplified)
        signal = signal; // No change for now

        // Stage 2: Instrumentation amplifier
        signal = this.inaTransferFunction(signal, frequency);

        // Stage 3: High-pass filter
        if (frequency < 0.5) {
            signal *= frequency / 0.5; // Simple high-pass response
        }

        // Stage 4: Low-pass filter
        if (frequency > 150) {
            signal *= Math.pow(150 / frequency, 4); // 4th order rolloff
        }

        // Stage 5: Notch filter (50Hz)
        signal = this.notchFilterTransferFunction(signal, frequency);

        // Stage 6: Output buffer
        signal = this.opampTransferFunction(signal, frequency);

        return signal;
    }

    // Generate frequency response
    generateFrequencyResponse(startFreq = 0.1, endFreq = 1000, points = 100) {
        const frequencies = [];
        const magnitude = [];
        const phase = [];

        const logStart = Math.log10(startFreq);
        const logEnd = Math.log10(endFreq);
        const logStep = (logEnd - logStart) / (points - 1);

        for (let i = 0; i < points; i++) {
            const freq = Math.pow(10, logStart + i * logStep);
            frequencies.push(freq);

            // Test with unit input
            const output = this.simulateSignalChain(1.0, freq);
            const mag = 20 * Math.log10(Math.abs(output)); // dB

            magnitude.push(mag);
            phase.push(0); // Simplified - no phase calculation
        }

        return { frequencies, magnitude, phase };
    }

    // Noise analysis
    analyzeNoise() {
        const noiseContributions = new Map();

        // INA128UA noise
        const inaVoltageNoise = 8e-9; // V/√Hz at 1kHz
        const inaCurrentNoise = 0.3e-12; // A/√Hz
        noiseContributions.set('INA128UA_voltage', inaVoltageNoise);
        noiseContributions.set('INA128UA_current', inaCurrentNoise);

        // Resistor thermal noise
        const kT = 4.14e-21; // kT at room temperature
        const resistorNoise = Math.sqrt(4 * kT * 1000); // 1kΩ resistor
        noiseContributions.set('resistor_thermal', resistorNoise);

        // Calculate total noise
        let totalNoise = 0;
        for (const [source, noise] of noiseContributions) {
            totalNoise += Math.pow(noise, 2);
        }
        totalNoise = Math.sqrt(totalNoise);

        return {
            contributions: noiseContributions,
            total: totalNoise,
            snr: 20 * Math.log10(1e-3 / totalNoise) // Assuming 1mV signal
        };
    }

    // Power analysis
    analyzePower() {
        const powerConsumption = new Map();

        // INA128UA: 700μA typical
        powerConsumption.set('INA128UA', 0.7e-3 * 9); // 9V supply

        // OPA2131UA: 900μA typical
        powerConsumption.set('OPA2131UA', 0.9e-3 * 9);

        // LM358: 500μA typical
        powerConsumption.set('LM358', 0.5e-3 * 9);

        // OP07D: 300μA typical
        powerConsumption.set('OP07D', 0.3e-3 * 9);

        // Calculate total power
        let totalPower = 0;
        for (const [component, power] of powerConsumption) {
            totalPower += power;
        }

        return {
            components: powerConsumption,
            total: totalPower,
            batteryLife: this.estimateBatteryLife(totalPower)
        };
    }

    estimateBatteryLife(powerConsumption) {
        // Assuming 9V battery with 500mAh capacity
        const batteryCapacity = 0.5; // Ah
        const batteryVoltage = 9; // V
        const batteryEnergy = batteryCapacity * batteryVoltage; // Wh

        const lifeHours = batteryEnergy / powerConsumption;
        return {
            hours: lifeHours,
            days: lifeHours / 24
        };
    }

    // Distortion analysis
    analyzeDistortion(inputAmplitude, frequency) {
        // Simplified THD calculation
        const fundamentalOutput = this.simulateSignalChain(inputAmplitude, frequency);

        // Estimate harmonics (simplified)
        const secondHarmonic = this.simulateSignalChain(inputAmplitude, 2 * frequency) * 0.001;
        const thirdHarmonic = this.simulateSignalChain(inputAmplitude, 3 * frequency) * 0.0005;

        const thd = Math.sqrt(Math.pow(secondHarmonic, 2) + Math.pow(thirdHarmonic, 2)) / fundamentalOutput;

        return {
            fundamental: fundamentalOutput,
            secondHarmonic: secondHarmonic,
            thirdHarmonic: thirdHarmonic,
            thd: thd * 100, // Percentage
            thdDb: 20 * Math.log10(thd)
        };
    }

    // Monte Carlo analysis for component tolerances
    monteCarloAnalysis(iterations = 1000) {
        const results = [];

        for (let i = 0; i < iterations; i++) {
            // Vary component values within tolerances
            const gainVariation = 1 + (Math.random() - 0.5) * 0.02; // ±1% tolerance
            const offsetVariation = (Math.random() - 0.5) * 100e-6; // ±50μV

            // Simulate with variations
            const output = this.simulateSignalChain(1e-3, 10) * gainVariation + offsetVariation;
            results.push(output);
        }

        // Calculate statistics
        const mean = results.reduce((a, b) => a + b) / results.length;
        const variance = results.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / results.length;
        const stdDev = Math.sqrt(variance);

        return {
            mean: mean,
            standardDeviation: stdDev,
            min: Math.min(...results),
            max: Math.max(...results),
            results: results
        };
    }
}

// Export for global access
window.CircuitSimulator = CircuitSimulator;

// Create global instance
window.circuitSimulator = new CircuitSimulator();

// Utility functions for circuit analysis
window.CircuitUtils = {
    // Convert dB to linear
    dbToLinear: function(db) {
        return Math.pow(10, db / 20);
    },

    // Convert linear to dB
    linearToDb: function(linear) {
        return 20 * Math.log10(Math.abs(linear));
    },

    // Parallel resistance calculation
    parallelResistance: function(r1, r2) {
        return (r1 * r2) / (r1 + r2);
    },

    // Voltage divider calculation
    voltageDivider: function(vin, r1, r2) {
        return vin * r2 / (r1 + r2);
    },

    // RC time constant
    rcTimeConstant: function(resistance, capacitance) {
        return resistance * capacitance;
    },

    // Cutoff frequency for RC circuit
    rcCutoffFrequency: function(resistance, capacitance) {
        return 1 / (2 * Math.PI * resistance * capacitance);
    }
};
