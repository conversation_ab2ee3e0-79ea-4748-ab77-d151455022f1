/* Virtual Workbench Styles */

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: white;
    padding: 1rem 0;
    margin-top: 70px;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: #667eea;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #764ba2;
}

.breadcrumb-separator {
    color: #999;
}

.breadcrumb-current {
    color: #333;
    font-weight: 600;
}

.workbench-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: #f5f5f5;
    padding-top: 0; /* Breadcrumb handles top spacing */
}

/* Toolbar Styles */
.workbench-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 100;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toolbar-btn {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.toolbar-btn:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-1px);
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle::after {
    content: ' ▼';
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    min-width: 180px;
    z-index: 1000;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 0.75rem 1rem;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #eee;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
}

.dropdown-menu a:last-child {
    border-bottom: none;
}

.dropdown-menu i {
    margin-right: 0.5rem;
    width: 16px;
}

.zoom-level {
    background: rgba(255,255,255,0.1);
    padding: 0.5rem;
    border-radius: 3px;
    font-size: 0.9rem;
    min-width: 50px;
    text-align: center;
}

/* Main Workbench Layout */
.workbench-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Component Library Sidebar */
.component-library {
    width: 280px;
    background: white;
    border-right: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.library-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.library-header h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
}

.library-search {
    position: relative;
}

.library-search input {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.library-search i {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.library-categories {
    border-bottom: 1px solid #eee;
}

.category {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    color: #666;
}

.category:hover {
    background: #f8f9fa;
    color: #333;
}

.category.active {
    background: #667eea;
    color: white;
}

.category i {
    margin-right: 0.5rem;
    width: 16px;
}

.component-list {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
}

.component-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.3s ease;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.component-item:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    transform: translateY(-1px);
}

.component-item:active {
    cursor: grabbing;
}

.component-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.component-icon {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 1.2rem;
    color: #667eea;
}

.component-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #333;
}

.component-info p {
    margin: 0;
    font-size: 0.8rem;
    color: #666;
}

/* Design Canvas */
.design-canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

.canvas-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.canvas-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab {
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px 5px 0 0;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.tab:hover {
    background: #f0f0f0;
}

.tab.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.tab i {
    margin-right: 0.5rem;
}

.canvas-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    background: white;
    border: 1px solid #ddd;
    color: #666;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.design-canvas {
    flex: 1;
    position: relative;
    overflow: auto;
    background: #fafafa;
}

.canvas-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
}

.canvas-content {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 600px;
}

/* Properties Panel */
.properties-panel {
    width: 300px;
    background: white;
    border-left: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.panel-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.properties-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.property-section {
    margin-bottom: 2rem;
}

.property-section h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
}

.property-item {
    margin-bottom: 1rem;
}

.property-item label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
    font-weight: 600;
    font-size: 0.9rem;
}

.property-item input,
.property-item select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.property-item input:focus,
.property-item select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Status Bar */
.status-bar {
    background: #2c3e50;
    color: white;
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
}

.status-section {
    display: flex;
    gap: 1.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-item i {
    width: 14px;
    opacity: 0.8;
}

.simulation-status #simulationIndicator {
    color: #27ae60;
}

.simulation-status.running #simulationIndicator {
    color: #f39c12;
    animation: pulse 1s infinite;
}

.simulation-status.error #simulationIndicator {
    color: #e74c3c;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Component Preview Modal */
.component-preview-content {
    max-width: 600px;
    margin: 0 auto;
}

.component-preview-content h3 {
    margin-bottom: 1.5rem;
    color: #333;
}

.preview-image {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: #666;
}

.preview-specs {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.preview-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Drag and Drop Styles */
.drop-zone {
    border: 2px dashed #667eea;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
}

.component-on-canvas {
    position: absolute;
    background: white;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 0.5rem;
    cursor: move;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: all 0.3s ease;
    min-width: 60px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: #333;
}

.component-on-canvas:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.component-on-canvas.selected {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.3);
}

.component-on-canvas.dragging {
    opacity: 0.8;
    transform: scale(1.05);
    z-index: 1000;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .component-library {
        width: 250px;
    }

    .properties-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .workbench-main {
        flex-direction: column;
    }

    .component-library,
    .properties-panel {
        width: 100%;
        height: 200px;
    }

    .toolbar-section {
        flex-wrap: wrap;
    }
}
