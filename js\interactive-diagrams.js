// Interactive Diagrams Control Functions

// Global state
let currentDiagram = 'block';
let animationEnabled = false;
let zoomLevel = 1.0;
let measurementMode = null;

// Diagram switching functions
function showDiagram(diagramType) {
    // Update tab appearance
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Hide all diagrams
    document.querySelectorAll('.diagram-container').forEach(container => {
        container.classList.remove('active');
    });
    
    // Show selected diagram
    const targetDiagram = document.getElementById(diagramType + 'Diagram');
    if (targetDiagram) {
        targetDiagram.classList.add('active');
    }
    
    currentDiagram = diagramType;
    
    // Render the appropriate diagram
    switch (diagramType) {
        case 'block':
            if (window.diagramRenderer) {
                diagramRenderer.renderBlockDiagram();
            }
            break;
        case 'schematic':
            if (window.diagramRenderer) {
                diagramRenderer.renderSchematicDiagram();
            }
            break;
        case 'signal-flow':
            if (window.diagramRenderer) {
                diagramRenderer.renderSignalFlowDiagram();
            }
            break;
    }
}

// Zoom and view controls
function zoomIn() {
    zoomLevel = Math.min(zoomLevel * 1.2, 3.0);
    applyZoom();
}

function zoomOut() {
    zoomLevel = Math.max(zoomLevel / 1.2, 0.3);
    applyZoom();
}

function resetView() {
    zoomLevel = 1.0;
    applyZoom();
    
    // Reset any pan offset
    const activeCanvas = document.querySelector('.diagram-container.active .block-diagram-canvas, .diagram-container.active .schematic-canvas, .diagram-container.active .signal-flow-canvas');
    if (activeCanvas) {
        activeCanvas.style.transform = `scale(${zoomLevel})`;
        activeCanvas.style.transformOrigin = 'center center';
    }
}

function applyZoom() {
    const activeCanvas = document.querySelector('.diagram-container.active .block-diagram-canvas, .diagram-container.active .schematic-canvas, .diagram-container.active .signal-flow-canvas');
    if (activeCanvas) {
        activeCanvas.style.transform = `scale(${zoomLevel})`;
        activeCanvas.style.transformOrigin = 'center center';
    }
}

// Animation controls
function toggleAnimation() {
    animationEnabled = !animationEnabled;
    const icon = document.getElementById('animationIcon');
    
    if (animationEnabled) {
        icon.className = 'fas fa-pause';
        if (window.diagramRenderer) {
            diagramRenderer.startSignalAnimation();
        }
        startContinuousAnimation();
    } else {
        icon.className = 'fas fa-play';
        if (window.diagramRenderer) {
            diagramRenderer.stopSignalAnimation();
        }
        stopContinuousAnimation();
    }
}

function startContinuousAnimation() {
    // Add pulsing effect to components
    const components = document.querySelectorAll('.block, .component');
    components.forEach((comp, index) => {
        setTimeout(() => {
            comp.style.animation = 'pulse 2s infinite';
        }, index * 200);
    });
    
    // Animate signal flow
    if (currentDiagram === 'signal-flow') {
        animateSignalFlow();
    }
}

function stopContinuousAnimation() {
    const components = document.querySelectorAll('.block, .component');
    components.forEach(comp => {
        comp.style.animation = '';
    });
}

function animateSignalFlow() {
    // Create animated signal indicators
    const canvas = document.querySelector('#signalFlowCanvas');
    if (!canvas) return;
    
    const indicator = document.createElement('div');
    indicator.className = 'signal-indicator';
    indicator.style.cssText = `
        position: absolute;
        width: 10px;
        height: 10px;
        background: #667eea;
        border-radius: 50%;
        animation: signalFlow 3s linear infinite;
        z-index: 1000;
    `;
    
    canvas.appendChild(indicator);
    
    // Remove after animation
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.parentNode.removeChild(indicator);
        }
    }, 3000);
}

// Export functionality
function exportDiagram() {
    const exportOptions = [
        { format: 'PNG', description: 'High-quality image' },
        { format: 'SVG', description: 'Scalable vector graphics' },
        { format: 'PDF', description: 'Portable document format' }
    ];
    
    let optionsHtml = '<h3>Export Options</h3>';
    exportOptions.forEach(option => {
        optionsHtml += `
            <button class="export-option" onclick="performExport('${option.format}')">
                <strong>${option.format}</strong><br>
                <small>${option.description}</small>
            </button>
        `;
    });
    
    showModal('Export Diagram', optionsHtml);
}

function performExport(format) {
    const activeCanvas = document.querySelector('.diagram-container.active');
    const diagramTitle = activeCanvas.querySelector('.diagram-header h2').textContent;
    
    switch (format) {
        case 'PNG':
            exportAsPNG(activeCanvas, diagramTitle);
            break;
        case 'SVG':
            exportAsSVG(activeCanvas, diagramTitle);
            break;
        case 'PDF':
            exportAsPDF(activeCanvas, diagramTitle);
            break;
    }
    
    closeModal();
}

function exportAsPNG(canvas, title) {
    // Use html2canvas library (would need to be included)
    alert(`PNG export functionality would capture the ${title} as a high-quality image.`);
}

function exportAsSVG(canvas, title) {
    // Extract SVG content if available
    const svg = canvas.querySelector('svg');
    if (svg) {
        const svgData = new XMLSerializer().serializeToString(svg);
        const blob = new Blob([svgData], { type: 'image/svg+xml' });
        downloadFile(blob, `${title.replace(/\s+/g, '_')}.svg`);
    } else {
        alert('SVG export not available for this diagram type.');
    }
}

function exportAsPDF(canvas, title) {
    alert(`PDF export functionality would create a document with the ${title}.`);
}

function downloadFile(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
}

// Layer toggle functions for schematic
function toggleLayer(layerName) {
    const isChecked = event.target.checked;
    const elements = document.querySelectorAll(`.${layerName}`);
    
    elements.forEach(element => {
        element.style.display = isChecked ? 'block' : 'none';
    });
    
    // Special handling for different layer types
    switch (layerName) {
        case 'components':
            toggleSchematicComponents(isChecked);
            break;
        case 'connections':
            toggleSchematicConnections(isChecked);
            break;
        case 'labels':
            toggleSchematicLabels(isChecked);
            break;
        case 'values':
            toggleSchematicValues(isChecked);
            break;
    }
}

function toggleSchematicComponents(show) {
    const components = document.querySelectorAll('#schematicCanvas .component');
    components.forEach(comp => {
        comp.style.opacity = show ? '1' : '0.3';
    });
}

function toggleSchematicConnections(show) {
    const wires = document.querySelectorAll('#schematicCanvas .wire');
    wires.forEach(wire => {
        wire.style.opacity = show ? '1' : '0.3';
    });
}

function toggleSchematicLabels(show) {
    const labels = document.querySelectorAll('#schematicCanvas text');
    labels.forEach(label => {
        if (label.getAttribute('font-weight') === 'bold') {
            label.style.opacity = show ? '1' : '0';
        }
    });
}

function toggleSchematicValues(show) {
    const values = document.querySelectorAll('#schematicCanvas text');
    values.forEach(value => {
        if (value.getAttribute('font-weight') !== 'bold') {
            value.style.opacity = show ? '1' : '0';
        }
    });
}

// Measurement functions
function measureVoltage() {
    setMeasurementMode('voltage');
    showMeasurementInstructions('Click on two points to measure voltage difference');
}

function measureCurrent() {
    setMeasurementMode('current');
    showMeasurementInstructions('Click on a component to measure current through it');
}

function measurePower() {
    setMeasurementMode('power');
    showMeasurementInstructions('Click on a component to measure power consumption');
}

function setMeasurementMode(mode) {
    measurementMode = mode;
    
    // Update cursor
    const canvas = document.querySelector('#schematicCanvas');
    if (canvas) {
        canvas.style.cursor = 'crosshair';
    }
    
    // Highlight measurement buttons
    document.querySelectorAll('.measure-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function showMeasurementInstructions(instruction) {
    const display = document.getElementById('measurementDisplay');
    const content = document.getElementById('measurementContent');
    
    content.innerHTML = `
        <div class="measurement-instruction">
            <i class="fas fa-info-circle"></i>
            <p>${instruction}</p>
        </div>
        <div class="measurement-results" id="measurementResults">
            <!-- Results will appear here -->
        </div>
    `;
    
    display.classList.add('open');
}

function performMeasurement(component, point) {
    if (!measurementMode) return;
    
    const results = document.getElementById('measurementResults');
    let measurementHtml = '';
    
    switch (measurementMode) {
        case 'voltage':
            const voltage = simulateVoltage(component);
            measurementHtml = `
                <div class="measurement-result">
                    <h4>Voltage Measurement</h4>
                    <p><strong>Component:</strong> ${component.id}</p>
                    <p><strong>Voltage:</strong> ${voltage.toFixed(3)}V</p>
                    <p><strong>Type:</strong> ${voltage > 0 ? 'Positive' : 'Negative'}</p>
                </div>
            `;
            break;
            
        case 'current':
            const current = simulateCurrent(component);
            measurementHtml = `
                <div class="measurement-result">
                    <h4>Current Measurement</h4>
                    <p><strong>Component:</strong> ${component.id}</p>
                    <p><strong>Current:</strong> ${current.toFixed(6)}A</p>
                    <p><strong>Direction:</strong> ${current > 0 ? 'Forward' : 'Reverse'}</p>
                </div>
            `;
            break;
            
        case 'power':
            const power = simulatePower(component);
            measurementHtml = `
                <div class="measurement-result">
                    <h4>Power Measurement</h4>
                    <p><strong>Component:</strong> ${component.id}</p>
                    <p><strong>Power:</strong> ${power.toFixed(6)}W</p>
                    <p><strong>Type:</strong> ${power > 0 ? 'Consuming' : 'Generating'}</p>
                </div>
            `;
            break;
    }
    
    results.innerHTML = measurementHtml;
}

function simulateVoltage(component) {
    // Simulate realistic voltage values based on component type
    const voltages = {
        'U1': 4.5 + Math.random() * 0.5, // INA128UA output
        'U2': 2.2 + Math.random() * 0.3, // OPA2131UA output
        'R1': 0.1 + Math.random() * 0.05, // Resistor voltage drop
        'R2': 0.1 + Math.random() * 0.05,
        'C1': 0.01 + Math.random() * 0.005, // Capacitor voltage
        'C2': 0.01 + Math.random() * 0.005
    };
    
    return voltages[component.id] || Math.random() * 5;
}

function simulateCurrent(component) {
    // Simulate realistic current values
    const currents = {
        'U1': 0.0007 + Math.random() * 0.0001, // 700μA typical
        'U2': 0.0009 + Math.random() * 0.0001, // 900μA typical
        'R1': 0.001 + Math.random() * 0.0002,
        'R2': 0.001 + Math.random() * 0.0002,
        'C1': 0.000001 + Math.random() * 0.0000005, // Very low leakage
        'C2': 0.000001 + Math.random() * 0.0000005
    };
    
    return currents[component.id] || Math.random() * 0.001;
}

function simulatePower(component) {
    const voltage = simulateVoltage(component);
    const current = simulateCurrent(component);
    return voltage * current;
}

// Signal flow update function
function updateSignalFlow() {
    if (currentDiagram === 'signal-flow' && window.diagramRenderer) {
        // Re-render signal flow with new parameters
        diagramRenderer.renderSignalFlowDiagram();
    }
}

// Panel control functions
function closeInfoPanel() {
    const panel = document.getElementById('infoPanel');
    panel.classList.remove('open');
}

function closeMeasurements() {
    const display = document.getElementById('measurementDisplay');
    display.classList.remove('open');
    measurementMode = null;
    
    // Reset cursor
    const canvas = document.querySelector('#schematicCanvas');
    if (canvas) {
        canvas.style.cursor = 'default';
    }
    
    // Remove active state from measurement buttons
    document.querySelectorAll('.measure-btn').forEach(btn => {
        btn.classList.remove('active');
    });
}

// Modal functions
function showModal(title, content) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('customModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'customModal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalTitle"></h3>
                    <button class="close-btn" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" id="modalBody"></div>
            </div>
        `;
        document.body.appendChild(modal);
    }
    
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalBody').innerHTML = content;
    modal.style.display = 'block';
}

function closeModal() {
    const modal = document.getElementById('customModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
            case '1':
                event.preventDefault();
                showDiagram('block');
                break;
            case '2':
                event.preventDefault();
                showDiagram('schematic');
                break;
            case '3':
                event.preventDefault();
                showDiagram('signal-flow');
                break;
            case '=':
            case '+':
                event.preventDefault();
                zoomIn();
                break;
            case '-':
                event.preventDefault();
                zoomOut();
                break;
            case '0':
                event.preventDefault();
                resetView();
                break;
        }
    }
    
    if (event.key === 'Escape') {
        closeInfoPanel();
        closeMeasurements();
        closeModal();
    }
});

// Mouse wheel zoom
document.addEventListener('wheel', function(event) {
    if (event.ctrlKey) {
        event.preventDefault();
        if (event.deltaY < 0) {
            zoomIn();
        } else {
            zoomOut();
        }
    }
});

// Add CSS for additional animations
const additionalStyles = document.createElement('style');
additionalStyles.textContent = `
    @keyframes signalFlow {
        0% { left: 0; top: 50%; }
        25% { left: 25%; top: 50%; }
        50% { left: 50%; top: 50%; }
        75% { left: 75%; top: 50%; }
        100% { left: 100%; top: 50%; }
    }
    
    .export-option {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
        padding: 1rem;
        background: #f8f9fa;
        border: 2px solid #ddd;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
    }
    
    .export-option:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
    
    .measurement-instruction {
        background: #e3f2fd;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .measurement-result {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }
    
    .measure-btn.active {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 2000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: 0;
        border-radius: 15px;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }
    
    .modal-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem;
        border-radius: 15px 15px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
`;
document.head.appendChild(additionalStyles);
