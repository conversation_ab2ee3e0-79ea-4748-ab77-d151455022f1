<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signal Analysis - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .signal-container {
            padding: 100px 0 50px;
            background: #f8f9fa;
        }
        
        .signal-controls {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .control-group label {
            font-weight: 600;
            color: #333;
        }
        
        .control-group input,
        .control-group select {
            padding: 0.5rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .control-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .plot-container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .plot-area {
            height: 400px;
            margin: 1rem 0;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .analysis-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0.5rem 0;
        }
        
        .metric-unit {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-normal { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-critical { background-color: #e74c3c; }
        
        .waveform-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .feature-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .feature-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .feature-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="#signals" class="nav-link active">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="pcb-viewer.html" class="nav-link">PCB Viewer</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Signal Analysis Content -->
    <div class="signal-container">
        <div class="container">
            <h1 class="section-title">ECG Signal Analysis</h1>
            
            <!-- Signal Controls -->
            <div class="signal-controls">
                <h2>Signal Parameters</h2>
                <div class="controls-grid">
                    <div class="control-group">
                        <label for="heartRate">Heart Rate (BPM)</label>
                        <input type="range" id="heartRate" min="30" max="200" value="72">
                        <span id="heartRateValue">72 BPM</span>
                    </div>
                    <div class="control-group">
                        <label for="amplitude">Signal Amplitude</label>
                        <input type="range" id="amplitude" min="0.1" max="2.0" value="1.0" step="0.1">
                        <span id="amplitudeValue">1.0x</span>
                    </div>
                    <div class="control-group">
                        <label for="noiseLevel">Noise Level</label>
                        <input type="range" id="noiseLevel" min="0" max="0.5" value="0.05" step="0.01">
                        <span id="noiseLevelValue">0.05</span>
                    </div>
                    <div class="control-group">
                        <label for="arrhythmia">Arrhythmia Type</label>
                        <select id="arrhythmia">
                            <option value="normal">Normal Sinus Rhythm</option>
                            <option value="bradycardia">Bradycardia</option>
                            <option value="tachycardia">Tachycardia</option>
                            <option value="atrial_fibrillation">Atrial Fibrillation</option>
                        </select>
                    </div>
                </div>
                
                <div class="control-buttons">
                    <button class="btn btn-primary" onclick="startRealTimeAnalysis()">
                        <i class="fas fa-play"></i> Start Real-time
                    </button>
                    <button class="btn btn-secondary" onclick="stopAnalysis()">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                    <button class="btn btn-outline" onclick="exportData()">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <button class="btn btn-outline" onclick="loadSampleData()">
                        <i class="fas fa-upload"></i> Load Sample
                    </button>
                </div>
            </div>

            <!-- Real-time ECG Plot -->
            <div class="plot-container">
                <h2>Real-time ECG Waveform</h2>
                <div class="plot-area" id="ecgPlot"></div>
            </div>

            <!-- Frequency Domain Analysis -->
            <div class="plot-container">
                <h2>Frequency Domain Analysis</h2>
                <div class="plot-area" id="fftPlot"></div>
            </div>

            <!-- Analysis Results -->
            <div class="analysis-grid">
                <!-- Heart Rate Analysis -->
                <div class="analysis-card">
                    <h3><i class="fas fa-heartbeat"></i> Heart Rate Analysis</h3>
                    <div class="metric-value" id="currentHeartRate">72</div>
                    <div class="metric-unit">BPM</div>
                    <div style="margin-top: 1rem;">
                        <span class="status-indicator status-normal" id="hrStatus"></span>
                        <span id="hrStatusText">Normal</span>
                    </div>
                    <div style="margin-top: 1rem;">
                        <small>Range: 60-100 BPM (Normal)</small><br>
                        <small>Variability: <span id="hrVariability">±2</span> BPM</small>
                    </div>
                </div>

                <!-- Signal Quality -->
                <div class="analysis-card">
                    <h3><i class="fas fa-signal"></i> Signal Quality</h3>
                    <div class="metric-value" id="signalQuality">95</div>
                    <div class="metric-unit">%</div>
                    <div style="margin-top: 1rem;">
                        <span class="status-indicator status-normal" id="sqStatus"></span>
                        <span id="sqStatusText">Excellent</span>
                    </div>
                    <div style="margin-top: 1rem;">
                        <small>SNR: <span id="snrValue">42</span> dB</small><br>
                        <small>Artifacts: <span id="artifactLevel">Low</span></small>
                    </div>
                </div>

                <!-- Waveform Features -->
                <div class="analysis-card">
                    <h3><i class="fas fa-wave-square"></i> Waveform Features</h3>
                    <div class="waveform-features">
                        <div class="feature-item">
                            <div class="feature-value" id="pWaveAmp">0.2</div>
                            <div class="feature-label">P Wave (mV)</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-value" id="qrsAmp">1.2</div>
                            <div class="feature-label">QRS (mV)</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-value" id="tWaveAmp">0.3</div>
                            <div class="feature-label">T Wave (mV)</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-value" id="prInterval">160</div>
                            <div class="feature-label">PR (ms)</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-value" id="qrsWidth">80</div>
                            <div class="feature-label">QRS Width (ms)</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-value" id="qtInterval">400</div>
                            <div class="feature-label">QT (ms)</div>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="analysis-card">
                    <h3><i class="fas fa-microchip"></i> System Status</h3>
                    <div style="margin: 1rem 0;">
                        <div style="margin-bottom: 0.5rem;">
                            <span class="status-indicator status-normal"></span>
                            ADC: <strong>Online</strong>
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <span class="status-indicator status-normal"></span>
                            Amplifier: <strong>Normal</strong>
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <span class="status-indicator status-normal"></span>
                            Filters: <strong>Active</strong>
                        </div>
                        <div style="margin-bottom: 0.5rem;">
                            <span class="status-indicator status-normal"></span>
                            Processing: <strong>Real-time</strong>
                        </div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <small>Sample Rate: 256 Hz</small><br>
                        <small>Buffer: <span id="bufferStatus">512/1024</span></small><br>
                        <small>Latency: <span id="latency">4</span> ms</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    <script src="../js/ecg-simulator.js"></script>
    <script src="../data/ecg-data.js"></script>
    <script>
        let analysisRunning = false;
        let analysisInterval = null;
        
        // Initialize signal analysis
        function initializeSignalAnalysis() {
            setupControlListeners();
            initializePlots();
            updateAnalysisDisplay();
        }
        
        function setupControlListeners() {
            // Heart rate control
            document.getElementById('heartRate').addEventListener('input', function() {
                const value = this.value;
                document.getElementById('heartRateValue').textContent = value + ' BPM';
                if (window.ecgSimulator) {
                    window.ecgSimulator.setHeartRate(parseInt(value));
                }
                updateAnalysisDisplay();
            });
            
            // Amplitude control
            document.getElementById('amplitude').addEventListener('input', function() {
                const value = this.value;
                document.getElementById('amplitudeValue').textContent = value + 'x';
                if (window.ecgSimulator) {
                    window.ecgSimulator.setAmplitude(parseFloat(value));
                }
            });
            
            // Noise level control
            document.getElementById('noiseLevel').addEventListener('input', function() {
                const value = this.value;
                document.getElementById('noiseLevelValue').textContent = value;
                if (window.ecgSimulator) {
                    window.ecgSimulator.setNoiseLevel(parseFloat(value));
                }
            });
            
            // Arrhythmia selection
            document.getElementById('arrhythmia').addEventListener('change', function() {
                if (window.ecgSimulator) {
                    window.ecgSimulator.simulateArrhythmia(this.value);
                }
                updateAnalysisDisplay();
            });
        }
        
        function initializePlots() {
            // Initialize ECG plot
            const ecgTrace = {
                x: [],
                y: [],
                type: 'scatter',
                mode: 'lines',
                line: { color: '#ff6b6b', width: 2 },
                name: 'ECG Signal'
            };
            
            const ecgLayout = {
                title: 'Real-time ECG Signal',
                xaxis: { title: 'Time (s)' },
                yaxis: { title: 'Amplitude (mV)' },
                margin: { t: 50, r: 50, b: 50, l: 60 }
            };
            
            Plotly.newPlot('ecgPlot', [ecgTrace], ecgLayout, {responsive: true});
            
            // Initialize FFT plot
            const fftTrace = {
                x: [],
                y: [],
                type: 'scatter',
                mode: 'lines',
                line: { color: '#667eea', width: 2 },
                name: 'Power Spectrum'
            };
            
            const fftLayout = {
                title: 'Frequency Spectrum',
                xaxis: { title: 'Frequency (Hz)', range: [0, 50] },
                yaxis: { title: 'Power (dB)' },
                margin: { t: 50, r: 50, b: 50, l: 60 }
            };
            
            Plotly.newPlot('fftPlot', [fftTrace], fftLayout, {responsive: true});
        }
        
        function startRealTimeAnalysis() {
            if (analysisRunning) return;
            
            analysisRunning = true;
            
            // Start ECG simulation
            if (window.ecgSimulator) {
                window.ecgSimulator.startSimulation(document.getElementById('ecgPlot'));
            }
            
            // Start analysis updates
            analysisInterval = setInterval(updateAnalysis, 1000);
            
            // Update button states
            document.querySelector('button[onclick="startRealTimeAnalysis()"]').disabled = true;
            document.querySelector('button[onclick="stopAnalysis()"]').disabled = false;
        }
        
        function stopAnalysis() {
            analysisRunning = false;
            
            if (window.ecgSimulator) {
                window.ecgSimulator.stopSimulation();
            }
            
            if (analysisInterval) {
                clearInterval(analysisInterval);
                analysisInterval = null;
            }
            
            // Update button states
            document.querySelector('button[onclick="startRealTimeAnalysis()"]').disabled = false;
            document.querySelector('button[onclick="stopAnalysis()"]').disabled = true;
        }
        
        function updateAnalysis() {
            if (!analysisRunning) return;
            
            // Simulate frequency domain analysis
            updateFFTPlot();
            
            // Update metrics
            updateAnalysisDisplay();
        }
        
        function updateFFTPlot() {
            // Generate sample frequency spectrum
            const frequencies = [];
            const power = [];
            
            for (let f = 0; f <= 50; f += 0.5) {
                frequencies.push(f);
                
                // Simulate ECG spectrum with peaks at harmonics
                let p = -60; // Base noise floor
                
                // Fundamental frequency (heart rate)
                const hr = parseInt(document.getElementById('heartRate').value);
                const f0 = hr / 60; // Hz
                
                if (Math.abs(f - f0) < 0.5) p += 20;
                if (Math.abs(f - 2*f0) < 0.5) p += 15;
                if (Math.abs(f - 3*f0) < 0.5) p += 10;
                
                // Add some random variation
                p += (Math.random() - 0.5) * 5;
                
                power.push(p);
            }
            
            Plotly.restyle('fftPlot', {
                x: [frequencies],
                y: [power]
            }, [0]);
        }
        
        function updateAnalysisDisplay() {
            const heartRate = parseInt(document.getElementById('heartRate').value);
            const arrhythmia = document.getElementById('arrhythmia').value;
            
            // Update heart rate display
            document.getElementById('currentHeartRate').textContent = heartRate;
            
            // Update heart rate status
            let hrStatus = 'normal';
            let hrStatusText = 'Normal';
            
            if (heartRate < 60) {
                hrStatus = 'warning';
                hrStatusText = 'Bradycardia';
            } else if (heartRate > 100) {
                hrStatus = 'warning';
                hrStatusText = 'Tachycardia';
            } else if (arrhythmia === 'atrial_fibrillation') {
                hrStatus = 'critical';
                hrStatusText = 'Atrial Fibrillation';
            }
            
            document.getElementById('hrStatus').className = `status-indicator status-${hrStatus}`;
            document.getElementById('hrStatusText').textContent = hrStatusText;
            
            // Update other metrics (simulated values)
            document.getElementById('signalQuality').textContent = Math.floor(85 + Math.random() * 15);
            document.getElementById('snrValue').textContent = Math.floor(35 + Math.random() * 15);
            document.getElementById('hrVariability').textContent = '±' + Math.floor(1 + Math.random() * 3);
            
            // Update waveform features
            document.getElementById('pWaveAmp').textContent = (0.15 + Math.random() * 0.1).toFixed(2);
            document.getElementById('qrsAmp').textContent = (1.0 + Math.random() * 0.4).toFixed(1);
            document.getElementById('tWaveAmp').textContent = (0.25 + Math.random() * 0.1).toFixed(2);
            document.getElementById('prInterval').textContent = Math.floor(140 + Math.random() * 40);
            document.getElementById('qrsWidth').textContent = Math.floor(70 + Math.random() * 20);
            document.getElementById('qtInterval').textContent = Math.floor(380 + Math.random() * 40);
        }
        
        function exportData() {
            if (window.ecgSimulator) {
                const data = window.ecgSimulator.exportData();
                const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'ecg_data_' + new Date().toISOString().slice(0,19).replace(/:/g, '-') + '.json';
                a.click();
                URL.revokeObjectURL(url);
            }
        }
        
        function loadSampleData() {
            // Load sample ECG data
            alert('Sample data loading functionality would be implemented here');
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeSignalAnalysis);
    </script>
</body>
</html>
