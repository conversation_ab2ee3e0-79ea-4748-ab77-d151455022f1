// Main JavaScript for ECG Signal System Virtual Simulation

// Global variables
let currentSection = 'home';
let ecgSimulator = null;
let componentModal = null;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    setupNavigation();
    setupModal();
    initializeECGPreview();
    setupSystemBlocks();
    setupResponsiveMenu();
    
    console.log('ECG Signal System Virtual Simulation initialized');
}

// Navigation setup
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Get target section
            const target = this.getAttribute('href').substring(1);
            navigateToSection(target);
        });
    });
}

// Navigate to specific section
function navigateToSection(section) {
    currentSection = section;
    
    // Smooth scroll to section
    const targetElement = document.getElementById(section);
    if (targetElement) {
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
    
    // Handle section-specific logic
    switch(section) {
        case 'circuit':
            loadCircuitSimulator();
            break;
        case 'signals':
            loadSignalAnalyzer();
            break;
        case 'pcb':
            loadPCBViewer();
            break;
        case 'about':
            loadAboutSection();
            break;
    }
}

// Modal setup
function setupModal() {
    componentModal = document.getElementById('componentModal');
    const closeBtn = document.querySelector('.close');
    
    closeBtn.addEventListener('click', closeModal);
    
    window.addEventListener('click', function(e) {
        if (e.target === componentModal) {
            closeModal();
        }
    });
}

// Open modal with component details
function openModal(componentType) {
    const modalBody = document.getElementById('modalBody');
    modalBody.innerHTML = getComponentDetails(componentType);
    componentModal.style.display = 'block';
}

// Close modal
function closeModal() {
    componentModal.style.display = 'none';
}

// Get component details for modal
function getComponentDetails(componentType) {
    const components = {
        electrodes: {
            title: 'ECG Electrodes',
            description: 'Ag/AgCl electrodes for bioelectric signal acquisition',
            specs: [
                'Material: Silver/Silver Chloride',
                'Impedance: < 2kΩ at 10Hz',
                'Offset Voltage: < 100μV',
                'Noise: < 50μVpp'
            ]
        },
        amplifier: {
            title: 'INA128UA Instrumentation Amplifier',
            description: 'High-precision, low-noise instrumentation amplifier for ECG signal conditioning',
            specs: [
                'Gain Range: 1 to 10,000',
                'Input Offset Voltage: 50μV max',
                'CMRR: 120dB min',
                'Input Bias Current: 5nA max',
                'Bandwidth: 200kHz'
            ]
        },
        filter: {
            title: 'Analog Filter Stages',
            description: 'Multi-stage filtering for noise reduction and signal conditioning',
            specs: [
                'High-pass: 0.5Hz (-3dB)',
                'Low-pass: 150Hz (-3dB)',
                'Notch: 50/60Hz rejection',
                'Total Gain: 1100x'
            ]
        },
        adc: {
            title: 'STM32F4 ADC',
            description: '12-bit successive approximation ADC for digital conversion',
            specs: [
                'Resolution: 12-bit',
                'Sampling Rate: up to 2.4 MSPS',
                'Input Range: 0-3.3V',
                'DNL: ±1 LSB',
                'INL: ±2 LSB'
            ]
        },
        processing: {
            title: 'Digital Signal Processing',
            description: 'Real-time digital filtering and analysis algorithms',
            specs: [
                'Sampling Frequency: 256Hz',
                'Digital Filtering: IIR/FIR',
                'Heart Rate Detection',
                'Arrhythmia Analysis'
            ]
        }
    };
    
    const component = components[componentType];
    if (!component) return '<p>Component details not found.</p>';
    
    return `
        <h2>${component.title}</h2>
        <p>${component.description}</p>
        <h3>Specifications:</h3>
        <ul>
            ${component.specs.map(spec => `<li>${spec}</li>`).join('')}
        </ul>
    `;
}

// Setup system block interactions
function setupSystemBlocks() {
    const systemBlocks = document.querySelectorAll('.system-block');
    
    systemBlocks.forEach(block => {
        block.addEventListener('click', function() {
            const componentType = this.getAttribute('data-component');
            openModal(componentType);
        });
    });
}

// Initialize ECG preview in hero section
function initializeECGPreview() {
    const previewContainer = document.getElementById('ecg-preview');
    if (previewContainer && typeof generateECGPreview === 'function') {
        generateECGPreview(previewContainer);
    }
}

// Responsive menu setup
function setupResponsiveMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Close menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
}

// Button click handlers
function startSimulation() {
    navigateToSection('signals');
    // Initialize full ECG simulation
    if (typeof initializeECGSimulation === 'function') {
        initializeECGSimulation();
    }
}

function viewComponents() {
    navigateToSection('circuit');
    // Load circuit component viewer
    if (typeof loadCircuitComponents === 'function') {
        loadCircuitComponents();
    }
}

// Load different sections
function loadCircuitSimulator() {
    console.log('Loading circuit simulator...');
    // This will be implemented in circuit-simulator.js
}

function loadSignalAnalyzer() {
    console.log('Loading signal analyzer...');
    // This will be implemented in ecg-simulator.js
}

function loadPCBViewer() {
    console.log('Loading PCB viewer...');
    // This will be implemented separately
}

function loadAboutSection() {
    console.log('Loading about section...');
    // Display technical documentation
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#27ae60' : '#3498db'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 3000;
        transform: translateX(400px);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(400px)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Application error:', e.error);
    showNotification('An error occurred. Please check the console for details.', 'error');
});

// Export functions for use in other modules
window.ECGApp = {
    navigateToSection,
    openModal,
    closeModal,
    showNotification,
    startSimulation,
    viewComponents
};
