// Virtual Workbench JavaScript

class VirtualWorkbench {
    constructor() {
        this.currentView = 'schematic';
        this.selectedTool = 'move';
        this.zoomLevel = 1.0;
        this.gridSize = 20;
        this.gridVisible = true;
        this.components = new Map();
        this.connections = [];
        this.selectedComponent = null;
        this.componentCounter = 0;
        this.undoStack = [];
        this.redoStack = [];

        this.initializeWorkbench();
    }

    initializeWorkbench() {
        this.setupEventListeners();
        this.populateComponentLibrary();
        this.updateStatusBar();
        this.setupCanvasInteraction();
    }

    setupEventListeners() {
        // Canvas mouse events
        const canvas = document.getElementById('canvasContent');
        canvas.addEventListener('click', this.handleCanvasClick.bind(this));
        canvas.addEventListener('mousemove', this.handleCanvasMouseMove.bind(this));
        canvas.addEventListener('contextmenu', this.handleCanvasRightClick.bind(this));

        // Component search
        const searchInput = document.getElementById('componentSearch');
        searchInput.addEventListener('input', this.filterComponents.bind(this));

        // Property panel updates
        this.setupPropertyListeners();

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }

    setupPropertyListeners() {
        const properties = ['componentName', 'componentValue', 'componentPackage', 'positionX', 'positionY', 'rotation'];
        properties.forEach(prop => {
            const element = document.getElementById(prop);
            if (element) {
                element.addEventListener('change', this.updateSelectedComponent.bind(this));
            }
        });
    }

    populateComponentLibrary() {
        const categories = {
            amplifiers: [
                { id: 'INA128UA', name: 'INA128UA', description: 'Instrumentation Amplifier', icon: 'fas fa-expand-arrows-alt' },
                { id: 'OPA2131UA', name: 'OPA2131UA', description: 'Precision Op-Amp', icon: 'fas fa-wave-square' },
                { id: 'LM358', name: 'LM358', description: 'Dual Op-Amp', icon: 'fas fa-wave-square' },
                { id: 'OP07D', name: 'OP07D', description: 'Precision Op-Amp', icon: 'fas fa-wave-square' }
            ],
            passives: [
                { id: 'RESISTOR', name: 'Resistor', description: 'Fixed Resistor', icon: 'fas fa-minus' },
                { id: 'CAPACITOR', name: 'Capacitor', description: 'Ceramic Capacitor', icon: 'fas fa-circle' },
                { id: 'INDUCTOR', name: 'Inductor', description: 'Fixed Inductor', icon: 'fas fa-circle-notch' },
                { id: 'POTENTIOMETER', name: 'Potentiometer', description: 'Variable Resistor', icon: 'fas fa-sliders-h' }
            ],
            power: [
                { id: 'A0509S', name: 'A0509S-1WR3', description: 'DC-DC Converter', icon: 'fas fa-bolt' },
                { id: 'BATTERY', name: '9V Battery', description: 'Power Source', icon: 'fas fa-battery-full' },
                { id: 'VREG', name: 'Voltage Regulator', description: 'Linear Regulator', icon: 'fas fa-microchip' }
            ],
            connectors: [
                { id: 'HEADER', name: 'Pin Header', description: '2.54mm Header', icon: 'fas fa-plug' },
                { id: 'JACK', name: 'Audio Jack', description: '3.5mm Jack', icon: 'fas fa-headphones' },
                { id: 'USB', name: 'USB Connector', description: 'USB Type-A', icon: 'fas fa-usb' }
            ],
            digital: [
                { id: 'STM32F4', name: 'STM32F4', description: 'ARM Microcontroller', icon: 'fas fa-microchip' },
                { id: 'CRYSTAL', name: 'Crystal', description: '8MHz Crystal', icon: 'fas fa-gem' },
                { id: 'LED', name: 'LED', description: 'Status LED', icon: 'fas fa-lightbulb' }
            ]
        };

        this.componentCategories = categories;
        this.showCategory('amplifiers'); // Show default category
    }

    showCategory(categoryName) {
        // Update active category
        document.querySelectorAll('.category').forEach(cat => cat.classList.remove('active'));
        event.target.classList.add('active');

        const componentList = document.getElementById('componentList');
        componentList.innerHTML = '';

        const components = this.componentCategories[categoryName] || [];
        components.forEach(component => {
            const componentElement = this.createComponentElement(component);
            componentList.appendChild(componentElement);
        });
    }

    createComponentElement(component) {
        const div = document.createElement('div');
        div.className = 'component-item';
        div.draggable = true;
        div.dataset.componentId = component.id;

        div.innerHTML = `
            <div class="component-icon">
                <i class="${component.icon}"></i>
            </div>
            <div class="component-info">
                <h4>${component.name}</h4>
                <p>${component.description}</p>
            </div>
        `;

        // Add drag event listeners
        div.addEventListener('dragstart', this.handleDragStart.bind(this));
        div.addEventListener('dragend', this.handleDragEnd.bind(this));
        div.addEventListener('dblclick', () => this.showComponentPreview(component));

        return div;
    }

    handleDragStart(event) {
        event.dataTransfer.setData('text/plain', event.target.dataset.componentId);
        event.target.classList.add('dragging');

        // Create drag image
        const dragImage = event.target.cloneNode(true);
        dragImage.style.transform = 'rotate(5deg)';
        document.body.appendChild(dragImage);
        event.dataTransfer.setDragImage(dragImage, 50, 25);
        setTimeout(() => document.body.removeChild(dragImage), 0);
    }

    handleDragEnd(event) {
        event.target.classList.remove('dragging');
    }

    setupCanvasInteraction() {
        const canvas = document.getElementById('canvasContent');

        // Drop zone setup
        canvas.addEventListener('dragover', (event) => {
            event.preventDefault();
            canvas.classList.add('drop-zone');
        });

        canvas.addEventListener('dragleave', () => {
            canvas.classList.remove('drop-zone');
        });

        canvas.addEventListener('drop', this.handleCanvasDrop.bind(this));
    }

    handleCanvasDrop(event) {
        event.preventDefault();
        const canvas = document.getElementById('canvasContent');
        canvas.classList.remove('drop-zone');

        const componentId = event.dataTransfer.getData('text/plain');
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        this.addComponentToCanvas(componentId, x, y);
    }

    addComponentToCanvas(componentId, x, y) {
        const componentData = this.findComponentData(componentId);
        if (!componentData) return;

        this.componentCounter++;
        const instanceId = `${componentId}_${this.componentCounter}`;

        const component = {
            id: instanceId,
            type: componentId,
            name: componentData.name,
            x: x,
            y: y,
            rotation: 0,
            properties: {
                value: this.getDefaultValue(componentId),
                package: this.getDefaultPackage(componentId)
            }
        };

        this.components.set(instanceId, component);
        this.renderComponent(component);
        this.updateStatusBar();
        this.saveState();
    }

    findComponentData(componentId) {
        for (const category of Object.values(this.componentCategories)) {
            const component = category.find(comp => comp.id === componentId);
            if (component) return component;
        }
        return null;
    }

    getDefaultValue(componentId) {
        const defaults = {
            'RESISTOR': '1kΩ',
            'CAPACITOR': '1μF',
            'INDUCTOR': '1mH',
            'INA128UA': 'INA128UA/2K5',
            'OPA2131UA': 'OPA2131UA/2K5',
            'LM358': 'LM358',
            'OP07D': 'OP07D'
        };
        return defaults[componentId] || '';
    }

    getDefaultPackage(componentId) {
        const packages = {
            'RESISTOR': 'R1206',
            'CAPACITOR': 'C1206',
            'INA128UA': 'SOIC-8',
            'OPA2131UA': 'SOIC-8',
            'LM358': 'SOP-8',
            'OP07D': 'SOP-8'
        };
        return packages[componentId] || '';
    }

    renderComponent(component) {
        const canvas = document.getElementById('canvasContent');
        const element = document.createElement('div');
        element.className = 'component-on-canvas';
        element.id = component.id;
        element.style.left = component.x + 'px';
        element.style.top = component.y + 'px';
        element.style.transform = `rotate(${component.rotation}deg)`;
        element.textContent = component.name;
        element.title = `${component.name} (${component.properties.value})`;

        // Add interaction events
        element.addEventListener('click', () => this.selectComponent(component.id));
        element.addEventListener('mousedown', this.startDragComponent.bind(this));
        element.addEventListener('dblclick', () => this.editComponent(component.id));

        canvas.appendChild(element);
    }

    selectComponent(componentId) {
        // Clear previous selection
        document.querySelectorAll('.component-on-canvas').forEach(el => {
            el.classList.remove('selected');
        });

        // Select new component
        const element = document.getElementById(componentId);
        if (element) {
            element.classList.add('selected');
            this.selectedComponent = componentId;
            this.updatePropertiesPanel();
        }
    }

    updatePropertiesPanel() {
        if (!this.selectedComponent) return;

        const component = this.components.get(this.selectedComponent);
        if (!component) return;

        document.getElementById('componentName').value = component.name;
        document.getElementById('componentValue').value = component.properties.value;
        document.getElementById('componentPackage').value = component.properties.package;
        document.getElementById('positionX').value = Math.round(component.x);
        document.getElementById('positionY').value = Math.round(component.y);
        document.getElementById('rotation').value = component.rotation;
    }

    updateSelectedComponent() {
        if (!this.selectedComponent) return;

        const component = this.components.get(this.selectedComponent);
        if (!component) return;

        // Update component properties
        component.name = document.getElementById('componentName').value;
        component.properties.value = document.getElementById('componentValue').value;
        component.properties.package = document.getElementById('componentPackage').value;
        component.x = parseFloat(document.getElementById('positionX').value) || component.x;
        component.y = parseFloat(document.getElementById('positionY').value) || component.y;
        component.rotation = parseFloat(document.getElementById('rotation').value) || component.rotation;

        // Update visual representation
        const element = document.getElementById(component.id);
        if (element) {
            element.style.left = component.x + 'px';
            element.style.top = component.y + 'px';
            element.style.transform = `rotate(${component.rotation}deg)`;
            element.textContent = component.name;
            element.title = `${component.name} (${component.properties.value})`;
        }

        this.saveState();
    }

    handleCanvasClick(event) {
        if (event.target.id === 'canvasContent') {
            // Clicked on empty canvas - deselect all
            this.selectedComponent = null;
            document.querySelectorAll('.component-on-canvas').forEach(el => {
                el.classList.remove('selected');
            });
            this.clearPropertiesPanel();
        }
    }

    handleCanvasMouseMove(event) {
        const rect = event.target.getBoundingClientRect();
        const x = Math.round(event.clientX - rect.left);
        const y = Math.round(event.clientY - rect.top);
        document.getElementById('cursorPosition').textContent = `X: ${x}, Y: ${y}`;
    }

    handleCanvasRightClick(event) {
        event.preventDefault();
        // Could implement context menu here
    }

    clearPropertiesPanel() {
        document.getElementById('componentName').value = '';
        document.getElementById('componentValue').value = '';
        document.getElementById('componentPackage').value = '';
        document.getElementById('positionX').value = '';
        document.getElementById('positionY').value = '';
        document.getElementById('rotation').value = '0';
    }

    updateStatusBar() {
        document.getElementById('componentCount').textContent = `Components: ${this.components.size}`;
        document.getElementById('connectionCount').textContent = `Connections: ${this.connections.length}`;
        document.getElementById('zoomLevel').textContent = `${Math.round(this.zoomLevel * 100)}%`;
    }

    saveState() {
        const state = {
            components: Array.from(this.components.entries()),
            connections: [...this.connections],
            timestamp: Date.now()
        };
        this.undoStack.push(state);
        if (this.undoStack.length > 50) {
            this.undoStack.shift(); // Limit undo history
        }
        this.redoStack = []; // Clear redo stack
    }

    filterComponents() {
        const searchTerm = document.getElementById('componentSearch').value.toLowerCase();
        const componentItems = document.querySelectorAll('.component-item');

        componentItems.forEach(item => {
            const name = item.querySelector('h4').textContent.toLowerCase();
            const description = item.querySelector('p').textContent.toLowerCase();

            if (name.includes(searchTerm) || description.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    handleKeyboardShortcuts(event) {
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'z':
                    event.preventDefault();
                    this.undo();
                    break;
                case 'y':
                    event.preventDefault();
                    this.redo();
                    break;
                case 's':
                    event.preventDefault();
                    this.saveProject();
                    break;
            }
        }

        if (event.key === 'Delete' && this.selectedComponent) {
            this.deleteSelectedComponent();
        }
    }

    deleteSelectedComponent() {
        if (!this.selectedComponent) return;

        const element = document.getElementById(this.selectedComponent);
        if (element) {
            element.remove();
        }

        this.components.delete(this.selectedComponent);
        this.selectedComponent = null;
        this.clearPropertiesPanel();
        this.updateStatusBar();
        this.saveState();
    }

    undo() {
        if (this.undoStack.length === 0) return;

        // Save current state to redo stack
        const currentState = {
            components: Array.from(this.components.entries()),
            connections: [...this.connections],
            timestamp: Date.now()
        };
        this.redoStack.push(currentState);

        // Restore previous state
        const previousState = this.undoStack.pop();
        this.restoreState(previousState);
    }

    redo() {
        if (this.redoStack.length === 0) return;

        // Save current state to undo stack
        const currentState = {
            components: Array.from(this.components.entries()),
            connections: [...this.connections],
            timestamp: Date.now()
        };
        this.undoStack.push(currentState);

        // Restore next state
        const nextState = this.redoStack.pop();
        this.restoreState(nextState);
    }

    restoreState(state) {
        // Clear canvas
        document.getElementById('canvasContent').innerHTML = '';

        // Restore components
        this.components = new Map(state.components);
        this.connections = [...state.connections];

        // Re-render components
        this.components.forEach(component => {
            this.renderComponent(component);
        });

        this.updateStatusBar();
    }

    loadProject(projectData) {
        try {
            // Validate project data
            if (!projectData || !projectData.components) {
                throw new Error('Invalid project file format');
            }

            // Clear current project
            this.components.clear();
            this.connections = [];
            document.getElementById('canvasContent').innerHTML = '';

            // Load components
            if (Array.isArray(projectData.components)) {
                this.components = new Map(projectData.components);
            }

            // Load connections
            if (Array.isArray(projectData.connections)) {
                this.connections = [...projectData.connections];
            }

            // Load settings
            if (projectData.settings) {
                this.currentView = projectData.settings.view || 'schematic';
                this.zoomLevel = projectData.settings.zoom || 1.0;
                this.gridVisible = projectData.settings.grid !== false;
            }

            // Re-render all components
            this.components.forEach(component => {
                this.renderComponent(component);
            });

            // Update UI
            this.updateStatusBar();
            this.clearPropertiesPanel();

            console.log('Project loaded successfully:', projectData.name || 'Unnamed Project');

        } catch (error) {
            console.error('Error loading project:', error);
            throw error;
        }
    }
}

// Global workbench instance
let workbench;

// Initialize workbench when page loads
document.addEventListener('DOMContentLoaded', function() {
    workbench = new VirtualWorkbench();
});

// Export for global access
window.VirtualWorkbench = VirtualWorkbench;

// Global functions for toolbar and menu actions
function toggleDropdown(menuId) {
    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== menuId) {
            menu.classList.remove('show');
        }
    });

    // Toggle the clicked dropdown
    const menu = document.getElementById(menuId);
    menu.classList.toggle('show');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.remove('show');
        });
    }
});

// File menu functions
function newProject() {
    if (confirm('Create a new project? This will clear the current design.')) {
        workbench.components.clear();
        workbench.connections = [];
        document.getElementById('canvasContent').innerHTML = '';
        workbench.updateStatusBar();
        workbench.saveState();
    }
}

function openProject() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const projectData = JSON.parse(e.target.result);
                    if (workbench && typeof workbench.loadProject === 'function') {
                        workbench.loadProject(projectData);
                    } else {
                        console.warn('Project loading not yet implemented');
                        alert('Project loading feature coming soon!');
                    }
                } catch (error) {
                    alert('Error loading project file: ' + error.message);
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
}

function saveProject() {
    const projectData = {
        name: 'ECG Circuit Design',
        version: '1.0',
        components: Array.from(workbench.components.entries()),
        connections: workbench.connections,
        settings: {
            view: workbench.currentView,
            zoom: workbench.zoomLevel,
            grid: workbench.gridVisible
        },
        timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(projectData, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ecg-circuit-design.json';
    a.click();
    URL.revokeObjectURL(url);
}

function exportProject() {
    const exportMenu = document.createElement('div');
    exportMenu.className = 'export-menu';
    exportMenu.innerHTML = `
        <div class="export-options">
            <h3>Export Options</h3>
            <button onclick="exportSchematic()">Export Schematic (SVG)</button>
            <button onclick="exportPCB()">Export PCB Layout (Gerber)</button>
            <button onclick="exportBOM()">Export Bill of Materials (CSV)</button>
            <button onclick="exportNetlist()">Export Netlist</button>
            <button onclick="closeExportMenu()">Cancel</button>
        </div>
    `;
    document.body.appendChild(exportMenu);
}

// View menu functions
function switchView(viewType) {
    workbench.currentView = viewType;

    // Update tab appearance
    document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
    document.querySelector(`[onclick="switchTab('${viewType}')"]`).classList.add('active');

    // Update canvas based on view
    const canvas = document.getElementById('canvasContent');
    canvas.className = `canvas-content view-${viewType}`;

    // Update status
    document.getElementById('currentLayer').textContent =
        viewType === 'pcb' ? 'PCB Layout' :
        viewType === '3d' ? '3D View' : 'Schematic';
}

function toggleGrid() {
    workbench.gridVisible = !workbench.gridVisible;
    const grid = document.getElementById('canvasGrid');
    grid.style.display = workbench.gridVisible ? 'block' : 'none';
}

// Simulation menu functions
function runSimulation() {
    const status = document.getElementById('simulationStatus');
    const indicator = document.getElementById('simulationIndicator');

    status.textContent = 'Running...';
    indicator.parentElement.className = 'status-item simulation-status running';

    // Simulate circuit analysis
    setTimeout(() => {
        status.textContent = 'Complete';
        indicator.parentElement.className = 'status-item simulation-status';
        showSimulationResults();
    }, 2000);
}

function stopSimulation() {
    const status = document.getElementById('simulationStatus');
    const indicator = document.getElementById('simulationIndicator');

    status.textContent = 'Stopped';
    indicator.parentElement.className = 'status-item simulation-status';
}

function showWaveforms() {
    // Open signal analysis page
    window.open('signal-analysis.html', '_blank');
}

function analyzeFrequency() {
    // Open circuit design page with frequency analysis
    window.open('circuit-design.html', '_blank');
}

// Tools menu functions
function selectTool(toolName) {
    workbench.selectedTool = toolName;

    // Update cursor based on tool
    const canvas = document.getElementById('canvasContent');
    canvas.style.cursor = {
        'wire': 'crosshair',
        'move': 'move',
        'rotate': 'grab',
        'delete': 'not-allowed'
    }[toolName] || 'default';
}

// Zoom functions
function zoomIn() {
    workbench.zoomLevel = Math.min(workbench.zoomLevel * 1.2, 5.0);
    applyZoom();
}

function zoomOut() {
    workbench.zoomLevel = Math.max(workbench.zoomLevel / 1.2, 0.1);
    applyZoom();
}

function fitToScreen() {
    workbench.zoomLevel = 1.0;
    applyZoom();
}

function applyZoom() {
    const canvas = document.getElementById('canvasContent');
    canvas.style.transform = `scale(${workbench.zoomLevel})`;
    canvas.style.transformOrigin = 'top left';
    workbench.updateStatusBar();
}

// Canvas control functions
function clearCanvas() {
    if (confirm('Clear the entire canvas? This action cannot be undone.')) {
        workbench.components.clear();
        workbench.connections = [];
        document.getElementById('canvasContent').innerHTML = '';
        workbench.selectedComponent = null;
        workbench.clearPropertiesPanel();
        workbench.updateStatusBar();
        workbench.saveState();
    }
}

function undoAction() {
    workbench.undo();
}

function redoAction() {
    workbench.redo();
}

// Tab switching
function switchTab(tabName) {
    switchView(tabName);
}

// Component preview functions
function showComponentPreview(component) {
    const modal = document.getElementById('componentPreviewModal');
    const title = document.getElementById('previewTitle');
    const image = document.getElementById('previewImage');
    const specs = document.getElementById('previewSpecs');

    title.textContent = component.name;
    image.innerHTML = `<i class="${component.icon}" style="font-size: 4rem; color: #667eea;"></i>`;

    // Get component specifications from ECG data
    const componentSpecs = getComponentSpecifications(component.id);
    specs.innerHTML = componentSpecs;

    modal.style.display = 'block';
    window.currentPreviewComponent = component;
}

function closeComponentPreview() {
    document.getElementById('componentPreviewModal').style.display = 'none';
}

function addComponentToCanvas() {
    if (window.currentPreviewComponent) {
        // Add component to center of canvas
        const canvas = document.getElementById('canvasContent');
        const centerX = canvas.clientWidth / 2;
        const centerY = canvas.clientHeight / 2;

        workbench.addComponentToCanvas(window.currentPreviewComponent.id, centerX, centerY);
        closeComponentPreview();
    }
}

function viewDatasheet() {
    if (window.currentPreviewComponent) {
        alert(`Datasheet for ${window.currentPreviewComponent.name} would open here.`);
    }
}

function getComponentSpecifications(componentId) {
    const specs = {
        'INA128UA': `
            <h4>Specifications:</h4>
            <ul>
                <li>Gain Range: 1 to 10,000</li>
                <li>CMRR: 120 dB min</li>
                <li>Input Offset: 50 μV max</li>
                <li>Bandwidth: 200 kHz</li>
                <li>Package: SOIC-8</li>
            </ul>
        `,
        'OPA2131UA': `
            <h4>Specifications:</h4>
            <ul>
                <li>Input Offset: 250 μV max</li>
                <li>Bandwidth: 4 MHz</li>
                <li>Slew Rate: 20 V/μs</li>
                <li>Noise: 5.5 nV/√Hz</li>
                <li>Package: SOIC-8</li>
            </ul>
        `,
        'RESISTOR': `
            <h4>Specifications:</h4>
            <ul>
                <li>Tolerance: ±1% to ±5%</li>
                <li>Power Rating: 1/4W</li>
                <li>Temperature Coefficient: ±100 ppm/°C</li>
                <li>Package: R1206</li>
            </ul>
        `,
        'CAPACITOR': `
            <h4>Specifications:</h4>
            <ul>
                <li>Tolerance: ±10% to ±20%</li>
                <li>Voltage Rating: 50V</li>
                <li>Dielectric: X7R</li>
                <li>Package: C1206</li>
            </ul>
        `
    };

    return specs[componentId] || '<p>No specifications available.</p>';
}

function showSimulationResults() {
    alert('Simulation completed successfully!\n\nResults:\n- Total Gain: 1100x\n- Bandwidth: 0.5-150 Hz\n- SNR: 62 dB\n- Power Consumption: 43.2 mW');
}
