// Console Test Script for ECG Signal System
// This script helps verify that all components are working correctly

function runSystemTest() {
    console.log('%c🧪 Running ECG System Test...', 'color: #667eea; font-size: 14px; font-weight: bold;');
    
    const results = {
        passed: 0,
        failed: 0,
        warnings: 0,
        tests: []
    };
    
    // Test 1: Check if error handler is loaded
    test('Error Handler', () => {
        return typeof window.errorHandler !== 'undefined';
    });
    
    // Test 2: Check if main app functions are available
    test('Main App Functions', () => {
        return typeof window.ECGApp !== 'undefined' && 
               typeof window.ECGApp.navigateToPage === 'function';
    });
    
    // Test 3: Check if ECG Simulator is available
    test('ECG Simulator', () => {
        return typeof ECGSimulator !== 'undefined';
    });
    
    // Test 4: Check if Plotly is loaded (warning if not)
    test('Plotly Library', () => {
        const available = typeof Plotly !== 'undefined';
        if (!available) {
            results.warnings++;
            console.warn('⚠️ Plotly not loaded - charts will use fallback displays');
        }
        return available;
    }, false); // Don't fail if <PERSON>lotly is missing
    
    // Test 5: Check required DOM elements
    test('Required DOM Elements', () => {
        const requiredElements = ['ecg-preview'];
        const missing = requiredElements.filter(id => !document.getElementById(id));
        if (missing.length > 0) {
            console.warn('⚠️ Missing elements:', missing);
            results.warnings++;
        }
        return missing.length === 0;
    }, false);
    
    // Test 6: Check if workbench is available (if on workbench page)
    if (window.location.pathname.includes('workbench')) {
        test('Virtual Workbench', () => {
            return typeof window.workbench !== 'undefined';
        });
    }
    
    // Test 7: Check if diagram renderer is available (if on diagrams page)
    if (window.location.pathname.includes('diagrams')) {
        test('Diagram Renderer', () => {
            return typeof window.diagramRenderer !== 'undefined';
        });
    }
    
    // Test 8: Check browser compatibility
    test('Browser Compatibility', () => {
        const features = [
            'fetch' in window,
            'Promise' in window,
            'addEventListener' in document,
            'querySelector' in document
        ];
        return features.every(feature => feature);
    });
    
    // Helper function to run individual tests
    function test(name, testFn, failOnError = true) {
        try {
            const result = testFn();
            if (result) {
                results.passed++;
                console.log(`✅ ${name}: PASSED`);
            } else {
                if (failOnError) {
                    results.failed++;
                    console.error(`❌ ${name}: FAILED`);
                } else {
                    results.warnings++;
                    console.warn(`⚠️ ${name}: WARNING`);
                }
            }
            results.tests.push({ name, result, type: result ? 'pass' : (failOnError ? 'fail' : 'warn') });
        } catch (error) {
            if (failOnError) {
                results.failed++;
                console.error(`❌ ${name}: ERROR -`, error.message);
            } else {
                results.warnings++;
                console.warn(`⚠️ ${name}: WARNING -`, error.message);
            }
            results.tests.push({ name, result: false, error: error.message, type: failOnError ? 'fail' : 'warn' });
        }
    }
    
    // Display final results
    setTimeout(() => {
        console.log('\n' + '='.repeat(50));
        console.log('%c📊 Test Results Summary', 'color: #333; font-size: 16px; font-weight: bold;');
        console.log('='.repeat(50));
        console.log(`✅ Passed: ${results.passed}`);
        console.log(`❌ Failed: ${results.failed}`);
        console.log(`⚠️ Warnings: ${results.warnings}`);
        console.log(`📝 Total Tests: ${results.tests.length}`);
        
        if (results.failed === 0) {
            console.log('%c🎉 All critical tests passed!', 'color: #4CAF50; font-size: 14px; font-weight: bold;');
        } else {
            console.log('%c⚠️ Some tests failed - check console for details', 'color: #ff6b6b; font-size: 14px; font-weight: bold;');
        }
        
        // Store results globally for inspection
        window.testResults = results;
        console.log('\n💡 Tip: Type "window.testResults" to see detailed test results');
        console.log('💡 Tip: Type "runSystemTest()" to run tests again');
    }, 100);
    
    return results;
}

// Auto-run test after page load (with delay to ensure everything is loaded)
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (window.location.search.includes('test=true') || 
            window.location.hostname === 'localhost' || 
            window.location.hostname === '127.0.0.1') {
            runSystemTest();
        }
    }, 2000);
});

// Make test function globally available
window.runSystemTest = runSystemTest;

// Add helpful console commands
console.log('%c💡 Console Commands Available:', 'color: #667eea; font-size: 12px; font-weight: bold;');
console.log('• runSystemTest() - Run comprehensive system test');
console.log('• errorHandler.getErrorReport() - Get error report');
console.log('• errorHandler.performSystemCheck() - Check system status');

// Performance monitoring
if (window.performance && window.performance.timing) {
    window.addEventListener('load', function() {
        setTimeout(() => {
            const timing = window.performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            console.log(`⏱️ Page load time: ${loadTime}ms`);
            
            if (loadTime > 3000) {
                console.warn('⚠️ Slow page load detected. Consider optimizing resources.');
            }
        }, 100);
    });
}
