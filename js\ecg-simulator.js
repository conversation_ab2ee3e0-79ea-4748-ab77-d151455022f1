// ECG Signal Simulator for Virtual Simulation Platform

class ECGSimulator {
    constructor() {
        this.sampleRate = 256; // Hz
        this.heartRate = 72; // BPM
        this.amplitude = 1.0;
        this.noiseLevel = 0.05;
        this.isRunning = false;
        this.currentTime = 0;
        this.dataBuffer = [];
        this.maxBufferSize = 1024;

        // ECG waveform parameters
        this.pWave = { amplitude: 0.25, duration: 0.09 };
        this.qrsComplex = { amplitude: 1.0, duration: 0.08 };
        this.tWave = { amplitude: 0.35, duration: 0.16 };
        this.prInterval = 0.16;
        this.qtInterval = 0.40;
    }

    // Generate ECG preview for hero section
    generatePreview(container) {
        if (!container) {
            console.warn('ECG preview container not found');
            return;
        }

        if (typeof Plotly === 'undefined') {
            console.warn('Plotly library not loaded, showing fallback ECG preview');
            this.showFallbackPreview(container);
            return;
        }

        try {
            const data = this.generateECGData(256, 1.0); // 1 second of data
            const time = Array.from({length: 256}, (_, i) => i / this.sampleRate);

            const trace = {
                x: time,
                y: data,
                type: 'scatter',
                mode: 'lines',
                line: {
                    color: '#ff6b6b',
                    width: 3
                },
                name: 'ECG Signal'
            };

            const layout = {
                title: {
                    text: 'Real-time ECG Signal',
                    font: { color: 'white', size: 16 }
                },
                xaxis: {
                    title: 'Time (s)',
                    color: 'white',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                yaxis: {
                    title: 'Amplitude (mV)',
                    color: 'white',
                    gridcolor: 'rgba(255,255,255,0.2)'
                },
                paper_bgcolor: 'rgba(0,0,0,0)',
                plot_bgcolor: 'rgba(0,0,0,0)',
                font: { color: 'white' },
                margin: { t: 40, r: 20, b: 40, l: 50 }
            };

            const config = {
                displayModeBar: false,
                responsive: true
            };

            Plotly.newPlot(container, [trace], layout, config);

            // Animate the preview
            this.animatePreview(container);
        } catch (error) {
            console.error('Error generating ECG preview:', error);
            this.showFallbackPreview(container);
        }
    }

    // Show fallback ECG preview when Plotly is not available
    showFallbackPreview(container) {
        container.innerHTML = `
            <div style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 200px;
                background: linear-gradient(135deg, rgba(255,107,107,0.1), rgba(255,107,107,0.3));
                border-radius: 10px;
                border: 2px solid rgba(255,107,107,0.5);
                color: white;
                text-align: center;
                font-family: Arial, sans-serif;
            ">
                <div>
                    <div style="font-size: 3rem; margin-bottom: 1rem;">
                        <i class="fas fa-heartbeat" style="color: #ff6b6b; animation: pulse 1s infinite;"></i>
                    </div>
                    <h3 style="margin: 0; font-size: 1.2rem;">ECG Signal Preview</h3>
                    <p style="margin: 0.5rem 0 0 0; opacity: 0.8;">Real-time ECG waveform simulation</p>
                </div>
            </div>
            <style>
                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }
            </style>
        `;
    }

    // Animate ECG preview
    animatePreview(container) {
        if (typeof Plotly === 'undefined') return;

        let frame = 0;
        const animate = () => {
            if (container.offsetParent === null) return; // Stop if container is hidden

            try {
                const data = this.generateECGData(256, 1.0);
                const time = Array.from({length: 256}, (_, i) => (i + frame * 10) / this.sampleRate);

                Plotly.restyle(container, {
                    x: [time],
                    y: [data]
                }, [0]);

                frame++;
                setTimeout(animate, 100); // Update every 100ms
            } catch (error) {
                console.error('Error animating ECG preview:', error);
                return; // Stop animation on error
            }
        };

        animate();
    }

    // Generate ECG data
    generateECGData(samples, duration) {
        const data = [];
        const dt = duration / samples;
        const beatDuration = 60 / this.heartRate; // seconds per beat

        for (let i = 0; i < samples; i++) {
            const t = i * dt;
            const beatPhase = (t % beatDuration) / beatDuration;

            let amplitude = this.generateECGBeat(beatPhase);

            // Add noise
            amplitude += (Math.random() - 0.5) * this.noiseLevel;

            data.push(amplitude * this.amplitude);
        }

        return data;
    }

    // Generate single ECG beat
    generateECGBeat(phase) {
        // Normalize phase to 0-1
        phase = phase % 1;

        let amplitude = 0;

        // P wave (0.0 - 0.15)
        if (phase >= 0.0 && phase <= 0.15) {
            const pPhase = (phase - 0.0) / 0.15;
            amplitude += this.pWave.amplitude * this.gaussianPulse(pPhase, 0.5, 0.3);
        }

        // QRS complex (0.2 - 0.35)
        if (phase >= 0.2 && phase <= 0.35) {
            const qrsPhase = (phase - 0.2) / 0.15;

            // Q wave (negative)
            if (qrsPhase <= 0.2) {
                amplitude -= 0.2 * this.qrsComplex.amplitude * (qrsPhase / 0.2);
            }
            // R wave (positive peak)
            else if (qrsPhase <= 0.6) {
                const rPhase = (qrsPhase - 0.2) / 0.4;
                amplitude += this.qrsComplex.amplitude * Math.sin(rPhase * Math.PI);
            }
            // S wave (negative)
            else {
                const sPhase = (qrsPhase - 0.6) / 0.4;
                amplitude -= 0.3 * this.qrsComplex.amplitude * Math.sin(sPhase * Math.PI);
            }
        }

        // T wave (0.5 - 0.8)
        if (phase >= 0.5 && phase <= 0.8) {
            const tPhase = (phase - 0.5) / 0.3;
            amplitude += this.tWave.amplitude * this.gaussianPulse(tPhase, 0.5, 0.4);
        }

        return amplitude;
    }

    // Gaussian pulse function
    gaussianPulse(x, center, width) {
        return Math.exp(-Math.pow((x - center) / width, 2));
    }

    // Start real-time simulation
    startSimulation(container) {
        if (this.isRunning) return;

        this.isRunning = true;
        this.currentTime = 0;
        this.dataBuffer = [];

        const updateInterval = 1000 / 30; // 30 FPS
        const samplesPerUpdate = Math.floor(this.sampleRate * updateInterval / 1000);

        const update = () => {
            if (!this.isRunning) return;

            // Generate new data points
            const newData = this.generateECGData(samplesPerUpdate, updateInterval / 1000);

            // Add to buffer
            this.dataBuffer.push(...newData);

            // Trim buffer if too large
            if (this.dataBuffer.length > this.maxBufferSize) {
                this.dataBuffer = this.dataBuffer.slice(-this.maxBufferSize);
            }

            // Update time array
            const timeArray = Array.from({length: this.dataBuffer.length},
                (_, i) => (this.currentTime - (this.dataBuffer.length - i - 1) * (1/this.sampleRate)));

            // Update plot
            Plotly.restyle(container, {
                x: [timeArray],
                y: [this.dataBuffer]
            }, [0]);

            this.currentTime += updateInterval / 1000;
            setTimeout(update, updateInterval);
        };

        update();
    }

    // Stop simulation
    stopSimulation() {
        this.isRunning = false;
    }

    // Set heart rate
    setHeartRate(bpm) {
        this.heartRate = Math.max(30, Math.min(200, bpm));
    }

    // Set amplitude
    setAmplitude(amp) {
        this.amplitude = Math.max(0.1, Math.min(2.0, amp));
    }

    // Set noise level
    setNoiseLevel(noise) {
        this.noiseLevel = Math.max(0, Math.min(0.5, noise));
    }

    // Add arrhythmia simulation
    simulateArrhythmia(type) {
        switch(type) {
            case 'atrial_fibrillation':
                this.heartRate = 60 + Math.random() * 40; // Irregular rate
                break;
            case 'bradycardia':
                this.heartRate = 45;
                break;
            case 'tachycardia':
                this.heartRate = 120;
                break;
            case 'normal':
            default:
                this.heartRate = 72;
                break;
        }
    }

    // Export data
    exportData() {
        return {
            sampleRate: this.sampleRate,
            heartRate: this.heartRate,
            data: this.dataBuffer,
            timestamp: new Date().toISOString()
        };
    }
}

// Global ECG simulator instance
let ecgSimulator = new ECGSimulator();

// Initialize ECG preview when page loads
function generateECGPreview(container) {
    ecgSimulator.generatePreview(container);
}

// Initialize full ECG simulation
function initializeECGSimulation() {
    // This function will be called when navigating to signals section
    console.log('Initializing full ECG simulation...');
}

// Export for global access
window.ECGSimulator = ECGSimulator;
window.ecgSimulator = ecgSimulator;
window.generateECGPreview = generateECGPreview;
window.initializeECGSimulation = initializeECGSimulation;
