// ECG Data and Component Specifications for Virtual Simulation

// Sample ECG data based on the MATLAB processing from the original project
const ECGData = {
    // Sample rate from the original MATLAB code
    sampleRate: 256, // Hz
    
    // Estimated parameters from the original getPWLSource.m
    adcResolution: 10, // 10-bit ADC (1024 levels)
    systemGain: 1100, // Total system gain from AD8232 configuration
    dcOffset: 0.37, // Estimated baseline offset
    
    // Component specifications from BOM
    components: {
        instrumentation_amplifier: {
            partNumber: "INA128UA/2K5",
            manufacturer: "Texas Instruments",
            specifications: {
                gainRange: "1 to 10,000",
                inputOffsetVoltage: "50μV max",
                cmrr: "120dB min",
                inputBiasCurrent: "5nA max",
                bandwidth: "200kHz",
                supplyVoltage: "±2.25V to ±18V",
                package: "SOIC-8"
            },
            description: "High-precision, low-noise instrumentation amplifier for ECG signal conditioning"
        },
        
        operational_amplifiers: {
            opa2131: {
                partNumber: "OPA2131UA/2K5",
                manufacturer: "Texas Instruments",
                specifications: {
                    inputOffsetVoltage: "250μV max",
                    inputBiasCurrent: "1pA typ",
                    bandwidth: "4MHz",
                    slewRate: "20V/μs",
                    package: "SOIC-8"
                }
            },
            lm358: {
                partNumber: "LM358",
                manufacturer: "Various",
                specifications: {
                    inputOffsetVoltage: "3mV max",
                    inputBiasCurrent: "45nA max",
                    bandwidth: "1MHz",
                    slewRate: "0.5V/μs",
                    package: "SOP-8"
                }
            },
            op07: {
                partNumber: "OP07D",
                manufacturer: "Various",
                specifications: {
                    inputOffsetVoltage: "25μV max",
                    inputBiasCurrent: "1.8nA max",
                    bandwidth: "600kHz",
                    slewRate: "0.3V/μs",
                    package: "SOP-8"
                }
            }
        },
        
        passive_components: {
            resistors: [
                { value: "1kΩ", quantity: 7, tolerance: "1%", package: "R1206" },
                { value: "10kΩ", quantity: 1, tolerance: "1%", package: "R1206" },
                { value: "390kΩ", quantity: 2, tolerance: "1%", package: "R1206" },
                { value: "3MΩ", quantity: 1, tolerance: "5%", package: "R1206" },
                { value: "1.6kΩ", quantity: 4, tolerance: "1%", package: "R1206" },
                { value: "15kΩ", quantity: 1, type: "Variable", package: "SMD" }
            ],
            capacitors: [
                { value: "1μF", quantity: 5, voltage: "50V", package: "C1206" },
                { value: "4.7μF", quantity: 1, voltage: "25V", package: "CASE-A_3216" },
                { value: "1μF", quantity: 2, voltage: "25V", package: "CASE-A_3216" }
            ]
        },
        
        power_supply: {
            partNumber: "A0509S-1WR3",
            manufacturer: "MORNSUN",
            specifications: {
                inputVoltage: "4.5-5.5V",
                outputVoltage: "±9V",
                outputCurrent: "±55mA",
                efficiency: "78%",
                isolation: "1500VDC"
            }
        },
        
        microcontroller: {
            partNumber: "STM32F4",
            manufacturer: "STMicroelectronics",
            specifications: {
                core: "ARM Cortex-M4",
                frequency: "168MHz",
                adcResolution: "12-bit",
                adcChannels: "16",
                adcSampleRate: "2.4MSPS",
                memory: "1MB Flash, 192KB RAM"
            }
        }
    },
    
    // Filter specifications
    filterDesign: {
        highPass: {
            cutoffFrequency: "0.5Hz",
            type: "Butterworth",
            order: 2,
            purpose: "Remove DC offset and baseline wander"
        },
        lowPass: {
            cutoffFrequency: "150Hz",
            type: "Butterworth", 
            order: 4,
            purpose: "Anti-aliasing and noise reduction"
        },
        notch: {
            frequency: "50Hz/60Hz",
            qFactor: 30,
            purpose: "Power line interference rejection"
        }
    },
    
    // Signal characteristics
    signalCharacteristics: {
        normalECG: {
            heartRate: "60-100 BPM",
            pWave: {
                amplitude: "0.1-0.3 mV",
                duration: "80-120 ms"
            },
            qrsComplex: {
                amplitude: "0.5-2.0 mV",
                duration: "60-100 ms"
            },
            tWave: {
                amplitude: "0.1-0.5 mV",
                duration: "120-200 ms"
            },
            prInterval: "120-200 ms",
            qtInterval: "350-450 ms"
        },
        
        artifacts: {
            muscleNoise: "20-200 Hz",
            motionArtifacts: "0.1-10 Hz",
            powerLineInterference: "50/60 Hz",
            baselineWander: "0.05-2 Hz"
        }
    },
    
    // Calibration data
    calibration: {
        adcReference: 3.3, // V
        adcBits: 12,
        gainStage1: 5, // INA128UA gain
        gainStage2: 220, // Total additional gain
        totalGain: 1100,
        inputRange: "±5mV",
        outputRange: "0-3.3V"
    },
    
    // Sample ECG waveform data points (normalized)
    sampleWaveform: [
        0.0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.0,
        -0.05, -0.1, -0.05, 0.0, 0.1, 0.3, 0.8, 1.0, 0.6, 0.2, -0.2, -0.1, 0.0,
        0.05, 0.1, 0.15, 0.2, 0.3, 0.4, 0.35, 0.3, 0.25, 0.2, 0.15, 0.1, 0.05, 0.0
    ],
    
    // Test signals for validation
    testSignals: {
        calibrationSquareWave: {
            amplitude: 1.0, // mV
            frequency: 1.0, // Hz
            purpose: "Gain and frequency response verification"
        },
        
        sinusoidalTest: {
            amplitude: 0.5, // mV
            frequency: 10.0, // Hz
            purpose: "Frequency response testing"
        },
        
        noiseFloor: {
            rmsValue: 0.01, // mV
            bandwidth: "0.5-150 Hz",
            purpose: "System noise characterization"
        }
    }
};

// Circuit simulation parameters
const CircuitParameters = {
    // Power supply voltages
    vcc: 9.0, // V
    vee: -9.0, // V
    vref: 0.0, // V
    
    // Operating point calculations
    biasVoltages: {
        ina128_vref: 1.65, // V (mid-supply)
        opamp_bias: 1.65, // V
        output_swing: "±8V"
    },
    
    // Frequency response
    frequencyResponse: {
        bandwidth: "0.5-150 Hz",
        gain: "60 dB (1100x)",
        phase: "Linear phase in passband",
        groupDelay: "< 10ms"
    },
    
    // Noise analysis
    noiseAnalysis: {
        inputReferredNoise: "2 μVrms",
        snr: "> 60 dB",
        enob: "10.5 bits",
        thd: "< 0.1%"
    }
};

// Export data for use in other modules
window.ECGData = ECGData;
window.CircuitParameters = CircuitParameters;

// Utility functions for data processing
window.ECGUtils = {
    // Convert ADC value to voltage
    adcToVoltage: function(adcValue) {
        return (adcValue / Math.pow(2, ECGData.calibration.adcBits)) * ECGData.calibration.adcReference;
    },
    
    // Convert voltage to ECG signal (mV)
    voltageToECG: function(voltage) {
        return ((voltage - ECGData.dcOffset) / ECGData.systemGain) * 1000; // Convert to mV
    },
    
    // Generate test signal
    generateTestSignal: function(type, samples, sampleRate) {
        const signal = [];
        const dt = 1.0 / sampleRate;
        
        for (let i = 0; i < samples; i++) {
            const t = i * dt;
            let value = 0;
            
            switch(type) {
                case 'square':
                    value = Math.sign(Math.sin(2 * Math.PI * ECGData.testSignals.calibrationSquareWave.frequency * t));
                    value *= ECGData.testSignals.calibrationSquareWave.amplitude;
                    break;
                case 'sine':
                    value = Math.sin(2 * Math.PI * ECGData.testSignals.sinusoidalTest.frequency * t);
                    value *= ECGData.testSignals.sinusoidalTest.amplitude;
                    break;
                case 'noise':
                    value = (Math.random() - 0.5) * ECGData.testSignals.noiseFloor.rmsValue * 2;
                    break;
            }
            
            signal.push(value);
        }
        
        return signal;
    }
};
