// Advanced Drag and Drop Functionality for Virtual Workbench

class DragDropManager {
    constructor(workbench) {
        this.workbench = workbench;
        this.isDragging = false;
        this.draggedElement = null;
        this.dragOffset = { x: 0, y: 0 };
        this.snapToGrid = true;
        this.gridSize = 20;
        
        this.initializeDragDrop();
    }
    
    initializeDragDrop() {
        this.setupCanvasDragDrop();
        this.setupComponentDragging();
    }
    
    setupCanvasDragDrop() {
        const canvas = document.getElementById('canvasContent');
        
        // Enhanced drop zone handling
        canvas.addEventListener('dragover', this.handleDragOver.bind(this));
        canvas.addEventListener('dragenter', this.handleDragEnter.bind(this));
        canvas.addEventListener('dragleave', this.handleDragLeave.bind(this));
        canvas.addEventListener('drop', this.handleDrop.bind(this));
    }
    
    setupComponentDragging() {
        // This will be called when components are added to canvas
        document.addEventListener('mousedown', this.handleMouseDown.bind(this));
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    }
    
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
        
        // Show drop indicator
        this.showDropIndicator(event);
    }
    
    handleDragEnter(event) {
        event.preventDefault();
        const canvas = document.getElementById('canvasContent');
        canvas.classList.add('drop-zone');
    }
    
    handleDragLeave(event) {
        // Only remove drop-zone class if leaving the canvas entirely
        if (!event.currentTarget.contains(event.relatedTarget)) {
            const canvas = document.getElementById('canvasContent');
            canvas.classList.remove('drop-zone');
            this.hideDropIndicator();
        }
    }
    
    handleDrop(event) {
        event.preventDefault();
        const canvas = document.getElementById('canvasContent');
        canvas.classList.remove('drop-zone');
        this.hideDropIndicator();
        
        const componentId = event.dataTransfer.getData('text/plain');
        const rect = canvas.getBoundingClientRect();
        let x = event.clientX - rect.left;
        let y = event.clientY - rect.top;
        
        // Snap to grid if enabled
        if (this.snapToGrid) {
            x = Math.round(x / this.gridSize) * this.gridSize;
            y = Math.round(y / this.gridSize) * this.gridSize;
        }
        
        // Add component with animation
        this.addComponentWithAnimation(componentId, x, y);
    }
    
    showDropIndicator(event) {
        const canvas = document.getElementById('canvasContent');
        const rect = canvas.getBoundingClientRect();
        let x = event.clientX - rect.left;
        let y = event.clientY - rect.top;
        
        if (this.snapToGrid) {
            x = Math.round(x / this.gridSize) * this.gridSize;
            y = Math.round(y / this.gridSize) * this.gridSize;
        }
        
        // Remove existing indicator
        this.hideDropIndicator();
        
        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = 'drop-indicator';
        indicator.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 60px;
            height: 40px;
            border: 2px dashed #667eea;
            background: rgba(102, 126, 234, 0.2);
            border-radius: 5px;
            pointer-events: none;
            z-index: 1000;
            animation: pulse 0.5s infinite alternate;
        `;
        
        canvas.appendChild(indicator);
    }
    
    hideDropIndicator() {
        const indicator = document.querySelector('.drop-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    addComponentWithAnimation(componentId, x, y) {
        // Create temporary element for animation
        const tempElement = document.createElement('div');
        tempElement.className = 'component-adding';
        tempElement.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 60px;
            height: 40px;
            background: white;
            border: 2px solid #667eea;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            color: #333;
            transform: scale(0);
            animation: componentAppear 0.3s ease-out forwards;
            z-index: 999;
        `;
        
        const componentData = this.workbench.findComponentData(componentId);
        tempElement.textContent = componentData ? componentData.name : componentId;
        
        const canvas = document.getElementById('canvasContent');
        canvas.appendChild(tempElement);
        
        // Remove temp element and add real component after animation
        setTimeout(() => {
            tempElement.remove();
            this.workbench.addComponentToCanvas(componentId, x, y);
        }, 300);
    }
    
    handleMouseDown(event) {
        const target = event.target;
        
        // Check if clicking on a component on canvas
        if (target.classList.contains('component-on-canvas')) {
            this.startComponentDrag(event, target);
        }
    }
    
    startComponentDrag(event, element) {
        event.preventDefault();
        
        this.isDragging = true;
        this.draggedElement = element;
        
        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('canvasContent').getBoundingClientRect();
        
        this.dragOffset = {
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
        };
        
        // Add dragging class
        element.classList.add('dragging');
        
        // Change cursor
        document.body.style.cursor = 'grabbing';
        
        // Prevent text selection
        document.body.style.userSelect = 'none';
    }
    
    handleMouseMove(event) {
        if (!this.isDragging || !this.draggedElement) return;
        
        event.preventDefault();
        
        const canvas = document.getElementById('canvasContent');
        const canvasRect = canvas.getBoundingClientRect();
        
        let x = event.clientX - canvasRect.left - this.dragOffset.x;
        let y = event.clientY - canvasRect.top - this.dragOffset.y;
        
        // Constrain to canvas bounds
        const elementRect = this.draggedElement.getBoundingClientRect();
        x = Math.max(0, Math.min(x, canvas.clientWidth - elementRect.width));
        y = Math.max(0, Math.min(y, canvas.clientHeight - elementRect.height));
        
        // Snap to grid if enabled
        if (this.snapToGrid) {
            x = Math.round(x / this.gridSize) * this.gridSize;
            y = Math.round(y / this.gridSize) * this.gridSize;
        }
        
        // Update element position
        this.draggedElement.style.left = x + 'px';
        this.draggedElement.style.top = y + 'px';
        
        // Update component data
        const componentId = this.draggedElement.id;
        const component = this.workbench.components.get(componentId);
        if (component) {
            component.x = x;
            component.y = y;
        }
        
        // Show snap guides
        this.showSnapGuides(x, y);
    }
    
    handleMouseUp(event) {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        
        if (this.draggedElement) {
            // Remove dragging class
            this.draggedElement.classList.remove('dragging');
            
            // Update properties panel if this component is selected
            if (this.draggedElement.classList.contains('selected')) {
                this.workbench.updatePropertiesPanel();
            }
            
            // Save state for undo
            this.workbench.saveState();
            
            this.draggedElement = null;
        }
        
        // Reset cursor and selection
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
        
        // Hide snap guides
        this.hideSnapGuides();
    }
    
    showSnapGuides(x, y) {
        this.hideSnapGuides();
        
        const canvas = document.getElementById('canvasContent');
        
        // Vertical guide
        const vGuide = document.createElement('div');
        vGuide.className = 'snap-guide vertical';
        vGuide.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: 0;
            width: 1px;
            height: 100%;
            background: #ff6b6b;
            pointer-events: none;
            z-index: 998;
            opacity: 0.7;
        `;
        
        // Horizontal guide
        const hGuide = document.createElement('div');
        hGuide.className = 'snap-guide horizontal';
        hGuide.style.cssText = `
            position: absolute;
            left: 0;
            top: ${y}px;
            width: 100%;
            height: 1px;
            background: #ff6b6b;
            pointer-events: none;
            z-index: 998;
            opacity: 0.7;
        `;
        
        canvas.appendChild(vGuide);
        canvas.appendChild(hGuide);
    }
    
    hideSnapGuides() {
        document.querySelectorAll('.snap-guide').forEach(guide => guide.remove());
    }
    
    enableSnapToGrid(enabled = true) {
        this.snapToGrid = enabled;
    }
    
    setGridSize(size) {
        this.gridSize = size;
        this.updateGridDisplay();
    }
    
    updateGridDisplay() {
        const grid = document.getElementById('canvasGrid');
        if (grid) {
            grid.style.backgroundSize = `${this.gridSize}px ${this.gridSize}px`;
        }
        
        // Update status bar
        document.getElementById('gridSize').textContent = `Grid: ${this.gridSize}px`;
    }
    
    // Multi-select functionality
    startMultiSelect(event) {
        // Implementation for selecting multiple components
        // Could be triggered by Ctrl+click or drag selection
    }
    
    // Component alignment helpers
    alignComponents(alignment) {
        const selectedComponents = document.querySelectorAll('.component-on-canvas.selected');
        if (selectedComponents.length < 2) return;
        
        const components = Array.from(selectedComponents);
        const bounds = this.getSelectionBounds(components);
        
        components.forEach(element => {
            const componentId = element.id;
            const component = this.workbench.components.get(componentId);
            if (!component) return;
            
            switch (alignment) {
                case 'left':
                    component.x = bounds.left;
                    element.style.left = bounds.left + 'px';
                    break;
                case 'right':
                    component.x = bounds.right - element.offsetWidth;
                    element.style.left = (bounds.right - element.offsetWidth) + 'px';
                    break;
                case 'top':
                    component.y = bounds.top;
                    element.style.top = bounds.top + 'px';
                    break;
                case 'bottom':
                    component.y = bounds.bottom - element.offsetHeight;
                    element.style.top = (bounds.bottom - element.offsetHeight) + 'px';
                    break;
                case 'center-h':
                    const centerX = bounds.left + (bounds.width / 2) - (element.offsetWidth / 2);
                    component.x = centerX;
                    element.style.left = centerX + 'px';
                    break;
                case 'center-v':
                    const centerY = bounds.top + (bounds.height / 2) - (element.offsetHeight / 2);
                    component.y = centerY;
                    element.style.top = centerY + 'px';
                    break;
            }
        });
        
        this.workbench.saveState();
    }
    
    getSelectionBounds(elements) {
        const rects = elements.map(el => el.getBoundingClientRect());
        const canvasRect = document.getElementById('canvasContent').getBoundingClientRect();
        
        const left = Math.min(...rects.map(r => r.left - canvasRect.left));
        const top = Math.min(...rects.map(r => r.top - canvasRect.top));
        const right = Math.max(...rects.map(r => r.right - canvasRect.left));
        const bottom = Math.max(...rects.map(r => r.bottom - canvasRect.top));
        
        return {
            left,
            top,
            right,
            bottom,
            width: right - left,
            height: bottom - top
        };
    }
    
    // Component distribution
    distributeComponents(direction) {
        const selectedComponents = document.querySelectorAll('.component-on-canvas.selected');
        if (selectedComponents.length < 3) return;
        
        const components = Array.from(selectedComponents);
        const bounds = this.getSelectionBounds(components);
        
        if (direction === 'horizontal') {
            components.sort((a, b) => a.offsetLeft - b.offsetLeft);
            const spacing = (bounds.width - components.reduce((sum, el) => sum + el.offsetWidth, 0)) / (components.length - 1);
            
            let currentX = bounds.left;
            components.forEach((element, index) => {
                if (index > 0) {
                    const componentId = element.id;
                    const component = this.workbench.components.get(componentId);
                    if (component) {
                        component.x = currentX;
                        element.style.left = currentX + 'px';
                    }
                }
                currentX += element.offsetWidth + spacing;
            });
        } else if (direction === 'vertical') {
            components.sort((a, b) => a.offsetTop - b.offsetTop);
            const spacing = (bounds.height - components.reduce((sum, el) => sum + el.offsetHeight, 0)) / (components.length - 1);
            
            let currentY = bounds.top;
            components.forEach((element, index) => {
                if (index > 0) {
                    const componentId = element.id;
                    const component = this.workbench.components.get(componentId);
                    if (component) {
                        component.y = currentY;
                        element.style.top = currentY + 'px';
                    }
                }
                currentY += element.offsetHeight + spacing;
            });
        }
        
        this.workbench.saveState();
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes componentAppear {
        from {
            transform: scale(0) rotate(180deg);
            opacity: 0;
        }
        to {
            transform: scale(1) rotate(0deg);
            opacity: 1;
        }
    }
    
    @keyframes pulse {
        from { opacity: 0.5; }
        to { opacity: 1; }
    }
    
    .component-on-canvas.dragging {
        z-index: 1000;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        transform: scale(1.05);
    }
    
    .drop-indicator {
        animation: pulse 0.5s infinite alternate;
    }
`;
document.head.appendChild(style);

// Initialize drag-drop manager when workbench is ready
document.addEventListener('DOMContentLoaded', function() {
    // Wait for workbench to be initialized
    setTimeout(() => {
        if (window.workbench) {
            window.dragDropManager = new DragDropManager(window.workbench);
        }
    }, 100);
});

// Export for global access
window.DragDropManager = DragDropManager;
