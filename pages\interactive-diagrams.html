<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Circuit Diagrams - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/diagrams.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/svg.js/3.1.2/svg.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="virtual-workbench.html" class="nav-link">Virtual Workbench</a>
                </li>
                <li class="nav-item">
                    <a href="#diagrams" class="nav-link active">Interactive Diagrams</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-nav">
        <div class="container">
            <nav class="breadcrumb">
                <a href="../index.html" class="breadcrumb-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Interactive Diagrams</span>
            </nav>
        </div>
    </div>

    <!-- Diagrams Container -->
    <div class="diagrams-container">
        <!-- Diagram Controls -->
        <div class="diagram-controls">
            <div class="control-section">
                <div class="diagram-tabs">
                    <button type="button" class="tab-btn active" onclick="showDiagram('block')">
                        <i class="fas fa-cubes"></i> Block Diagram
                    </button>
                    <button type="button" class="tab-btn" onclick="showDiagram('schematic')">
                        <i class="fas fa-project-diagram"></i> Schematic Diagram
                    </button>
                    <button type="button" class="tab-btn" onclick="showDiagram('signal-flow')">
                        <i class="fas fa-stream"></i> Signal Flow
                    </button>
                </div>
            </div>

            <div class="control-section">
                <div class="diagram-tools">
                    <button type="button" class="tool-btn" onclick="zoomIn()" title="Zoom In">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button type="button" class="tool-btn" onclick="zoomOut()" title="Zoom Out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button type="button" class="tool-btn" onclick="resetView()" title="Reset View">
                        <i class="fas fa-home"></i>
                    </button>
                    <button type="button" class="tool-btn" onclick="toggleAnimation()" title="Toggle Animation">
                        <i class="fas fa-play" id="animationIcon"></i>
                    </button>
                    <button type="button" class="tool-btn" onclick="exportDiagram()" title="Export Diagram">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Block Diagram -->
        <div id="blockDiagram" class="diagram-container active">
            <div class="diagram-header">
                <h2><i class="fas fa-cubes"></i> ECG Signal System - Block Diagram</h2>
                <p>Interactive system overview with signal flow visualization</p>
            </div>

            <div class="block-diagram-canvas" id="blockCanvas">
                <!-- Block diagram will be rendered here -->
            </div>

            <div class="diagram-legend">
                <h3>Legend</h3>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-color input"></div>
                        <span>Input Stage</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color processing"></div>
                        <span>Signal Processing</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color output"></div>
                        <span>Output Stage</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color power"></div>
                        <span>Power Supply</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Schematic Diagram -->
        <div id="schematicDiagram" class="diagram-container">
            <div class="diagram-header">
                <h2><i class="fas fa-project-diagram"></i> ECG Signal System - Schematic Diagram</h2>
                <p>Detailed electrical schematic with interactive components</p>
            </div>

            <div class="schematic-controls">
                <div class="layer-controls">
                    <label>
                        <input type="checkbox" checked onchange="toggleLayer('components')"> Components
                    </label>
                    <label>
                        <input type="checkbox" checked onchange="toggleLayer('connections')"> Connections
                    </label>
                    <label>
                        <input type="checkbox" checked onchange="toggleLayer('labels')"> Labels
                    </label>
                    <label>
                        <input type="checkbox" checked onchange="toggleLayer('values')"> Values
                    </label>
                </div>

                <div class="measurement-tools">
                    <button type="button" class="measure-btn" onclick="measureVoltage()">
                        <i class="fas fa-bolt"></i> Voltage
                    </button>
                    <button type="button" class="measure-btn" onclick="measureCurrent()">
                        <i class="fas fa-wave-square"></i> Current
                    </button>
                    <button type="button" class="measure-btn" onclick="measurePower()">
                        <i class="fas fa-battery-half"></i> Power
                    </button>
                </div>
            </div>

            <div class="schematic-canvas" id="schematicCanvas">
                <!-- Schematic diagram will be rendered here -->
            </div>
        </div>

        <!-- Signal Flow Diagram -->
        <div id="signalFlowDiagram" class="diagram-container">
            <div class="diagram-header">
                <h2><i class="fas fa-stream"></i> ECG Signal Flow Analysis</h2>
                <p>Real-time signal transformation through each stage</p>
            </div>

            <div class="signal-flow-canvas" id="signalFlowCanvas">
                <!-- Signal flow diagram will be rendered here -->
            </div>

            <div class="signal-controls">
                <div class="signal-input">
                    <h4>Input Signal</h4>
                    <label>Heart Rate (BPM): <input type="range" id="heartRate" min="60" max="120" value="72" oninput="updateSignalFlow()"></label>
                    <label>Amplitude (mV): <input type="range" id="amplitude" min="0.5" max="2.0" value="1.0" step="0.1" oninput="updateSignalFlow()"></label>
                    <label>Noise Level: <input type="range" id="noiseLevel" min="0" max="0.2" value="0.05" step="0.01" oninput="updateSignalFlow()"></label>
                </div>
            </div>
        </div>
    </div>

    <!-- Component Info Panel -->
    <div class="info-panel" id="infoPanel">
        <div class="info-header">
            <h3 id="infoTitle">Component Information</h3>
            <button type="button" class="close-btn" onclick="closeInfoPanel()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="info-content" id="infoContent">
            <p>Click on any component to view detailed information</p>
        </div>
    </div>

    <!-- Measurement Display -->
    <div class="measurement-display" id="measurementDisplay">
        <div class="measurement-header">
            <h4>Measurements</h4>
            <button type="button" class="close-btn" onclick="closeMeasurements()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="measurement-content" id="measurementContent">
            <!-- Measurement results will be displayed here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    <script src="../js/diagram-renderer.js"></script>
    <script src="../js/interactive-diagrams.js"></script>
    <script src="../data/ecg-data.js"></script>
</body>
</html>
