/* Interactive Diagrams Styles */

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: white;
    padding: 1rem 0;
    margin-top: 70px;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: #667eea;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #764ba2;
}

.breadcrumb-separator {
    color: #999;
}

.breadcrumb-current {
    color: #333;
    font-weight: 600;
}

.diagrams-container {
    padding-top: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Diagram Controls */
.diagram-controls {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 70px;
    z-index: 100;
}

.control-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.diagram-tabs {
    display: flex;
    gap: 0.5rem;
}

.tab-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #666;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #333;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.diagram-tools {
    display: flex;
    gap: 0.5rem;
}

.tool-btn {
    background: white;
    border: 2px solid #ddd;
    color: #666;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
}

/* Diagram Containers */
.diagram-container {
    display: none;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.diagram-container.active {
    display: block;
}

.diagram-header {
    text-align: center;
    margin-bottom: 2rem;
}

.diagram-header h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.diagram-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Block Diagram Styles */
.block-diagram-canvas {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.block {
    position: absolute;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    min-width: 150px;
    text-align: center;
}

.block:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.block.input {
    border-color: #4CAF50;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.block.processing {
    border-color: #2196F3;
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.block.output {
    border-color: #FF9800;
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
}

.block.power {
    border-color: #9C27B0;
    background: linear-gradient(135deg, #9C27B0, #7B1FA2);
    color: white;
}

.block-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.block-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.block-description {
    font-size: 0.9rem;
    opacity: 0.9;
}

.connection-line {
    position: absolute;
    background: #333;
    z-index: 1;
    transition: all 0.3s ease;
}

.connection-line.animated {
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: flowAnimation 2s linear infinite;
}

@keyframes flowAnimation {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.connection-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    z-index: 2;
}

.connection-arrow.right {
    border-left: 10px solid #333;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
}

.connection-arrow.down {
    border-top: 10px solid #333;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
}

/* Schematic Diagram Styles */
.schematic-controls {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.layer-controls {
    display: flex;
    gap: 1.5rem;
}

.layer-controls label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    color: #333;
}

.layer-controls input[type="checkbox"] {
    transform: scale(1.2);
    accent-color: #667eea;
}

.measurement-tools {
    display: flex;
    gap: 0.5rem;
}

.measure-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.measure-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.schematic-canvas {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    min-height: 700px;
    position: relative;
}

/* Signal Flow Styles */
.signal-flow-canvas {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    min-height: 600px;
    margin-bottom: 2rem;
}

.signal-controls {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.signal-input h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.signal-input label {
    display: block;
    margin-bottom: 1rem;
    color: #666;
    font-weight: 600;
}

.signal-input input[type="range"] {
    width: 200px;
    margin-left: 1rem;
    accent-color: #667eea;
}

/* Legend Styles */
.diagram-legend {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.diagram-legend h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.legend-items {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 2px solid #ddd;
}

.legend-color.input { background: linear-gradient(135deg, #4CAF50, #45a049); }
.legend-color.processing { background: linear-gradient(135deg, #2196F3, #1976D2); }
.legend-color.output { background: linear-gradient(135deg, #FF9800, #F57C00); }
.legend-color.power { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }

/* Info Panel */
.info-panel {
    position: fixed;
    right: -400px;
    top: 50%;
    transform: translateY(-50%);
    width: 350px;
    background: white;
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 0 20px rgba(0,0,0,0.2);
    transition: right 0.3s ease;
    z-index: 1000;
    max-height: 80vh;
    overflow-y: auto;
}

.info-panel.open {
    right: 0;
}

.info-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 15px 0 0 0;
}

.info-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.info-content {
    padding: 1.5rem;
}

/* Measurement Display */
.measurement-display {
    position: fixed;
    bottom: -300px;
    left: 50%;
    transform: translateX(-50%);
    width: 600px;
    background: white;
    border-radius: 15px 15px 0 0;
    box-shadow: 0 -5px 20px rgba(0,0,0,0.2);
    transition: bottom 0.3s ease;
    z-index: 1000;
}

.measurement-display.open {
    bottom: 0;
}

.measurement-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 15px 15px 0 0;
}

.measurement-header h4 {
    margin: 0;
    font-size: 1.2rem;
}

.measurement-content {
    padding: 1.5rem;
    max-height: 200px;
    overflow-y: auto;
}

/* Component Styles */
.component {
    position: absolute;
    cursor: pointer;
    transition: all 0.3s ease;
}

.component:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 5px 10px rgba(0,0,0,0.3));
}

.component.highlighted {
    filter: drop-shadow(0 0 10px #667eea);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Wire Styles */
.wire {
    stroke: #333;
    stroke-width: 2;
    fill: none;
    transition: all 0.3s ease;
}

.wire.animated {
    stroke: #667eea;
    stroke-width: 3;
    animation: wireFlow 2s linear infinite;
}

@keyframes wireFlow {
    0% { stroke-dasharray: 0 10; }
    100% { stroke-dasharray: 10 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .diagram-controls {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .control-section {
        width: 100%;
        justify-content: center;
    }

    .diagram-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }

    .info-panel {
        width: 90%;
        right: -90%;
    }

    .measurement-display {
        width: 90%;
    }

    .legend-items {
        justify-content: center;
    }
}
