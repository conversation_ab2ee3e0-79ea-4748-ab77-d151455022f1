<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Signal System - Virtual Simulation</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link active">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#circuit" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="#signals" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="#pcb" class="nav-link">PCB Viewer</a>
                </li>
                <li class="nav-item">
                    <a href="#about" class="nav-link">Technical Specs</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">ECG Signal System</h1>
                <h2 class="hero-subtitle">Virtual Simulation & Design Platform</h2>
                <p class="hero-description">
                    Explore the complete ECG signal acquisition system including circuit design,
                    signal processing, and PCB implementation. Interactive simulations for
                    biomedical engineering education.
                </p>
                <div class="hero-buttons">
                    <button type="button" class="btn btn-primary" onclick="startSimulation()">
                        <i class="fas fa-play"></i> Start Simulation
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="viewComponents()">
                        <i class="fas fa-microchip"></i> View Components
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="ecg-preview" id="ecg-preview">
                    <!-- ECG waveform will be rendered here -->
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">System Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <h3>Signal Processing</h3>
                    <p>Real-time ECG signal generation and analysis with customizable parameters</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3>Circuit Design</h3>
                    <p>Interactive circuit components including INA128UA amplifier and filtering stages</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3>PCB Layout</h3>
                    <p>Visualize the physical PCB design with component placement and routing</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Data Analysis</h3>
                    <p>Advanced signal analysis tools with frequency domain visualization</p>
                </div>
            </div>
        </div>
    </section>

    <!-- System Overview Section -->
    <section class="system-overview">
        <div class="container">
            <h2 class="section-title">System Architecture</h2>
            <div class="system-diagram">
                <div class="system-block" data-component="electrodes">
                    <i class="fas fa-user"></i>
                    <span>ECG Electrodes</span>
                </div>
                <div class="arrow">→</div>
                <div class="system-block" data-component="amplifier">
                    <i class="fas fa-expand-arrows-alt"></i>
                    <span>INA128UA Amplifier</span>
                </div>
                <div class="arrow">→</div>
                <div class="system-block" data-component="filter">
                    <i class="fas fa-filter"></i>
                    <span>Analog Filters</span>
                </div>
                <div class="arrow">→</div>
                <div class="system-block" data-component="adc">
                    <i class="fas fa-microchip"></i>
                    <span>STM32F4 ADC</span>
                </div>
                <div class="arrow">→</div>
                <div class="system-block" data-component="processing">
                    <i class="fas fa-laptop"></i>
                    <span>Digital Processing</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Access Section -->
    <section class="quick-access">
        <div class="container">
            <h2 class="section-title">Quick Access</h2>
            <div class="access-grid">
                <div class="access-card" onclick="navigateToSection('circuit')">
                    <div class="access-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>Circuit Simulator</h3>
                    <p>Interactive circuit design and component analysis</p>
                    <button type="button" class="btn btn-outline">Explore Circuit</button>
                </div>
                <div class="access-card" onclick="navigateToSection('signals')">
                    <div class="access-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3>ECG Signals</h3>
                    <p>Real-time ECG waveform generation and analysis</p>
                    <button type="button" class="btn btn-outline">Analyze Signals</button>
                </div>
                <div class="access-card" onclick="navigateToSection('pcb')">
                    <div class="access-icon">
                        <i class="fas fa-memory"></i>
                    </div>
                    <h3>PCB Design</h3>
                    <p>View PCB layout and component placement</p>
                    <button type="button" class="btn btn-outline">View PCB</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Specifications Section -->
    <section id="about" class="technical-specs">
        <div class="container">
            <h2 class="section-title">Technical Specifications</h2>

            <!-- System Performance -->
            <div class="spec-category">
                <h3><i class="fas fa-tachometer-alt"></i> System Performance</h3>
                <div class="spec-grid">
                    <div class="spec-card">
                        <h4>Signal Acquisition</h4>
                        <ul class="spec-list">
                            <li><strong>Input Range:</strong> ±5 mV (differential)</li>
                            <li><strong>Input Impedance:</strong> >10 MΩ</li>
                            <li><strong>CMRR:</strong> >120 dB @ 50/60 Hz</li>
                            <li><strong>Input Offset:</strong> <50 μV</li>
                            <li><strong>Noise (RTI):</strong> <2 μVrms (0.5-150 Hz)</li>
                        </ul>
                    </div>
                    <div class="spec-card">
                        <h4>Signal Processing</h4>
                        <ul class="spec-list">
                            <li><strong>Total Gain:</strong> 1100x (60.8 dB)</li>
                            <li><strong>Bandwidth:</strong> 0.5 - 150 Hz (-3dB)</li>
                            <li><strong>Sample Rate:</strong> 256 Hz</li>
                            <li><strong>Resolution:</strong> 12-bit ADC</li>
                            <li><strong>SNR:</strong> >60 dB</li>
                        </ul>
                    </div>
                    <div class="spec-card">
                        <h4>Power Consumption</h4>
                        <ul class="spec-list">
                            <li><strong>Supply Voltage:</strong> ±9V (18V total)</li>
                            <li><strong>Total Current:</strong> <3 mA</li>
                            <li><strong>Power Dissipation:</strong> <54 mW</li>
                            <li><strong>Battery Life:</strong> >150 hours (9V, 500mAh)</li>
                            <li><strong>Standby Current:</strong> <10 μA</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Component Specifications -->
            <div class="spec-category">
                <h3><i class="fas fa-microchip"></i> Key Components</h3>
                <div class="component-details-grid">
                    <div class="component-detail-card">
                        <div class="component-header">
                            <i class="fas fa-expand-arrows-alt"></i>
                            <h4>INA128UA - Instrumentation Amplifier</h4>
                        </div>
                        <div class="component-specs">
                            <table class="specs-table">
                                <tr><td>Manufacturer:</td><td>Texas Instruments</td></tr>
                                <tr><td>Package:</td><td>SOIC-8</td></tr>
                                <tr><td>Gain Range:</td><td>1 to 10,000 V/V</td></tr>
                                <tr><td>Gain Setting:</td><td>G = 1 + (50kΩ/RG)</td></tr>
                                <tr><td>CMRR:</td><td>120 dB min @ DC</td></tr>
                                <tr><td>Input Offset Voltage:</td><td>50 μV max</td></tr>
                                <tr><td>Input Bias Current:</td><td>5 nA max</td></tr>
                                <tr><td>Bandwidth:</td><td>200 kHz (G=100)</td></tr>
                                <tr><td>Supply Range:</td><td>±2.25V to ±18V</td></tr>
                                <tr><td>Quiescent Current:</td><td>700 μA typ</td></tr>
                            </table>
                        </div>
                    </div>

                    <div class="component-detail-card">
                        <div class="component-header">
                            <i class="fas fa-wave-square"></i>
                            <h4>OPA2131UA - Precision Op-Amp</h4>
                        </div>
                        <div class="component-specs">
                            <table class="specs-table">
                                <tr><td>Manufacturer:</td><td>Texas Instruments</td></tr>
                                <tr><td>Package:</td><td>SOIC-8 (Dual)</td></tr>
                                <tr><td>Input Offset Voltage:</td><td>250 μV max</td></tr>
                                <tr><td>Input Bias Current:</td><td>1 pA typ</td></tr>
                                <tr><td>Bandwidth (GBW):</td><td>4 MHz</td></tr>
                                <tr><td>Slew Rate:</td><td>20 V/μs</td></tr>
                                <tr><td>Input Noise Voltage:</td><td>5.5 nV/√Hz @ 1kHz</td></tr>
                                <tr><td>Supply Range:</td><td>±2.25V to ±18V</td></tr>
                                <tr><td>Quiescent Current:</td><td>900 μA typ</td></tr>
                                <tr><td>Application:</td><td>Active Filters</td></tr>
                            </table>
                        </div>
                    </div>

                    <div class="component-detail-card">
                        <div class="component-header">
                            <i class="fas fa-microchip"></i>
                            <h4>STM32F4 - Microcontroller</h4>
                        </div>
                        <div class="component-specs">
                            <table class="specs-table">
                                <tr><td>Core:</td><td>ARM Cortex-M4F</td></tr>
                                <tr><td>Clock Speed:</td><td>168 MHz</td></tr>
                                <tr><td>Flash Memory:</td><td>1 MB</td></tr>
                                <tr><td>SRAM:</td><td>192 KB</td></tr>
                                <tr><td>ADC Resolution:</td><td>12-bit</td></tr>
                                <tr><td>ADC Channels:</td><td>16 channels</td></tr>
                                <tr><td>Max Sample Rate:</td><td>2.4 MSPS</td></tr>
                                <tr><td>ADC Reference:</td><td>3.3V</td></tr>
                                <tr><td>DSP Instructions:</td><td>Yes (FPU)</td></tr>
                                <tr><td>Real-time Processing:</td><td>256 Hz ECG sampling</td></tr>
                            </table>
                        </div>
                    </div>

                    <div class="component-detail-card">
                        <div class="component-header">
                            <i class="fas fa-bolt"></i>
                            <h4>A0509S-1WR3 - DC-DC Converter</h4>
                        </div>
                        <div class="component-specs">
                            <table class="specs-table">
                                <tr><td>Manufacturer:</td><td>MORNSUN</td></tr>
                                <tr><td>Input Voltage:</td><td>4.5 - 5.5V</td></tr>
                                <tr><td>Output Voltage:</td><td>±9V</td></tr>
                                <tr><td>Output Current:</td><td>±55 mA</td></tr>
                                <tr><td>Power Rating:</td><td>1W</td></tr>
                                <tr><td>Efficiency:</td><td>78% typ</td></tr>
                                <tr><td>Isolation:</td><td>1500 VDC</td></tr>
                                <tr><td>Ripple & Noise:</td><td>50 mVp-p</td></tr>
                                <tr><td>Operating Temp:</td><td>-40°C to +85°C</td></tr>
                                <tr><td>Package:</td><td>SIP-4</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Design -->
            <div class="spec-category">
                <h3><i class="fas fa-filter"></i> Analog Filter Design</h3>
                <div class="filter-grid">
                    <div class="filter-card">
                        <h4>High-Pass Filter (0.5 Hz)</h4>
                        <div class="filter-details">
                            <p><strong>Purpose:</strong> Remove DC offset and baseline wander</p>
                            <p><strong>Type:</strong> 2nd Order Butterworth</p>
                            <p><strong>Implementation:</strong> Sallen-Key topology</p>
                            <p><strong>Components:</strong> R4, R5 (390kΩ), C6, C8 (100nF)</p>
                            <p><strong>Cutoff Frequency:</strong> fc = 1/(2π√(R4×R5×C6×C8))</p>
                            <p><strong>Attenuation:</strong> 40 dB/decade below 0.5 Hz</p>
                        </div>
                    </div>
                    <div class="filter-card">
                        <h4>Low-Pass Filter (150 Hz)</h4>
                        <div class="filter-details">
                            <p><strong>Purpose:</strong> Anti-aliasing and noise reduction</p>
                            <p><strong>Type:</strong> 4th Order Butterworth</p>
                            <p><strong>Implementation:</strong> Cascaded Sallen-Key stages</p>
                            <p><strong>Components:</strong> R7, R8 (1.6kΩ), C7, C9 (10μF)</p>
                            <p><strong>Cutoff Frequency:</strong> fc = 1/(2π×R×C)</p>
                            <p><strong>Attenuation:</strong> 80 dB/decade above 150 Hz</p>
                        </div>
                    </div>
                    <div class="filter-card">
                        <h4>Notch Filter (50/60 Hz)</h4>
                        <div class="filter-details">
                            <p><strong>Purpose:</strong> Power line interference rejection</p>
                            <p><strong>Type:</strong> Twin-T notch filter</p>
                            <p><strong>Q Factor:</strong> 30 (narrow notch)</p>
                            <p><strong>Rejection:</strong> >40 dB @ 50Hz/60Hz</p>
                            <p><strong>Bandwidth:</strong> ±1.7 Hz @ -3dB</p>
                            <p><strong>Components:</strong> Precision resistors and capacitors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ECG Signal System</h3>
                    <p>Virtual simulation platform for biomedical engineering education</p>
                </div>
                <div class="footer-section">
                    <h3>Components</h3>
                    <ul>
                        <li>INA128UA Instrumentation Amplifier</li>
                        <li>LM358 Operational Amplifiers</li>
                        <li>STM32F4 Microcontroller</li>
                        <li>Analog Filtering Circuits</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Resources</h3>
                    <ul>
                        <li><a href="#circuit">Circuit Documentation</a></li>
                        <li><a href="#signals">Signal Processing</a></li>
                        <li><a href="#pcb">PCB Design Files</a></li>
                        <li><a href="#about">Technical Specifications</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 ECG Signal System. Educational Use.</p>
            </div>
        </div>
    </footer>

    <!-- Modal for Component Details -->
    <div id="componentModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody">
                <!-- Component details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/ecg-simulator.js"></script>
    <script src="data/ecg-data.js"></script>
</body>
</html>
